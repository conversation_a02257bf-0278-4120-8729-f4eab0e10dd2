<?php

namespace App\Http\Controllers;

use App\Models\Repair;
use App\Models\PaymentSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Illuminate\Support\Facades\Session;

class GuestController extends Controller
{
    /**
     * Show the guest login form
     */
    public function showLoginForm()
    {
        return view('guest.login');
    }

    /**
     * Process the guest login by tracking a repair
     */
    public function login(Request $request)
    {
        $validated = $request->validate([
            'special_key' => 'required|string|size:8',
        ]);

        $repair = Repair::where('special_key', strtoupper($validated['special_key']))
            ->with(['items.device.customer', 'items.service'])
            ->first();

        if (!$repair) {
            return back()->withErrors([
                'special_key' => 'The tracking key you entered is invalid. Please check and try again.',
            ])->withInput();
        }

        // Store the repair special key in the session
        Session::put('guest_repair_key', $repair->special_key);

        return redirect()->route('guest.dashboard');
    }

    /**
     * Show the guest dashboard with repair status
     */
    public function dashboard()
    {
        // Set timezone to Philippine time
        date_default_timezone_set('Asia/Manila');
        
        // Check if the user has a repair key in session
        if (!Session::has('guest_repair_key')) {
            return redirect()->route('guest.login')
                ->with('error', 'Please enter your tracking key to view repair status.');
        }

        $specialKey = Session::get('guest_repair_key');
        $repair = Repair::where('special_key', $specialKey)
            ->with(['items.device.customer', 'items.service'])
            ->first();

        if (!$repair) {
            // If the repair is not found (maybe deleted), remove from session and redirect
            Session::forget('guest_repair_key');
            return redirect()->route('guest.login')
                ->with('error', 'Your repair tracking information could not be found. Please try again.');
        }

        // Get available payment methods
        $enabledPaymentMethods = PaymentSetting::getEnabledMethods();
        $digitalMethods = $enabledPaymentMethods->whereNotIn('payment_method', ['cash']);
        $forceCashPayment = $digitalMethods->isEmpty();

        return view('guest.dashboard', compact('repair', 'enabledPaymentMethods', 'forceCashPayment'));
    }

    /**
     * Logout the guest
     */
    public function logout()
    {
        Session::forget('guest_repair_key');
        return redirect()->route('guest.login')
            ->with('success', 'You have been logged out successfully.');
    }

    /**
     * Get the latest repair status via AJAX
     */
    public function getRepairStatus(Request $request)
    {
        $specialKey = $request->query('key');
        
        if (!$specialKey) {
            return response()->json([
                'success' => false,
                'message' => 'No tracking key provided'
            ], 400);
        }
        
        $repair = Repair::where('special_key', $specialKey)
            ->with(['items.device.customer', 'items.service'])
            ->first();
        
        if (!$repair) {
            return response()->json([
                'success' => false,
                'message' => 'Repair not found'
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'repair' => [
                'status' => $repair->status,
                'payment_status' => $repair->payment_status ?? 'unpaid',
                'started_at' => $repair->started_at ? $repair->started_at->timezone('Asia/Manila')->format('M d, Y - h:i A') : null,
                'completed_at' => $repair->completed_at ? $repair->completed_at->timezone('Asia/Manila')->format('M d, Y - h:i A') : null,
                'duration' => $repair->duration
            ]
        ]);
    }
}
