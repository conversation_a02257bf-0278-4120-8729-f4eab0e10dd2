<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\PaymentSetting;

class PaymentSettingsController extends Controller
{
    /**
     * Display payment settings page
     */
    public function index()
    {
        $paymentMethods = PaymentSetting::getAllMethods();
        return view('payment-settings.index', compact('paymentMethods'));
    }

    /**
     * Update payment method status
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'is_enabled' => 'required|boolean'
        ]);

        $paymentMethod = PaymentSetting::findOrFail($id);
        $paymentMethod->update([
            'is_enabled' => $request->is_enabled
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Payment method status updated successfully'
        ]);
    }

    /**
     * Get enabled payment methods for API
     */
    public function getEnabledMethods()
    {
        $enabledMethods = PaymentSetting::getEnabledMethods();

        // Check if all digital payment methods are disabled
        $digitalMethods = $enabledMethods->whereNotIn('payment_method', ['cash']);
        $allDigitalDisabled = $digitalMethods->isEmpty();

        return response()->json([
            'methods' => $enabledMethods,
            'force_cash' => $allDigitalDisabled,
            'message' => $allDigitalDisabled ? 'All digital payment methods are disabled. Cash payment will be used.' : null
        ]);
    }

    /**
     * Update payment method configuration
     */
    public function updateConfiguration(Request $request, $id)
    {
        $paymentMethod = PaymentSetting::findOrFail($id);

        $request->validate([
            'configuration' => 'required|array'
        ]);

        $paymentMethod->update([
            'configuration' => $request->configuration
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Payment method configuration updated successfully'
        ]);
    }
}
