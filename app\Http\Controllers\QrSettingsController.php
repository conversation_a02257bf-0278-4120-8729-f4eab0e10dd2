<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\QrSetting;

class QrSettingsController extends Controller
{
    /**
     * Display QR settings page
     */
    public function index()
    {
        $gcashEnabled = QrSetting::isGcashEnabled();
        $paymayaEnabled = QrSetting::isPaymayaEnabled();

        return view('qr-settings.index', compact('gcashEnabled', 'paymayaEnabled'));
    }

    /**
     * Toggle QR payment method
     */
    public function toggle(Request $request)
    {
        $request->validate([
            'method' => 'required|in:gcash,paymaya',
            'enabled' => 'required|boolean'
        ]);

        QrSetting::toggleMethod($request->method, $request->enabled);

        return response()->json([
            'success' => true,
            'message' => ucfirst($request->method) . ' QR code ' . ($request->enabled ? 'enabled' : 'disabled') . ' successfully'
        ]);
    }

    /**
     * Get current QR settings for API
     */
    public function getSettings()
    {
        return response()->json([
            'gcash_enabled' => QrSetting::isGcashEnabled(),
            'paymaya_enabled' => QrSetting::isPaymayaEnabled(),
            'enabled_methods' => QrSetting::getEnabledMethods()
        ]);
    }
}
