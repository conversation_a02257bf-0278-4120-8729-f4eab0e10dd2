<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PaymentSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'payment_method',
        'is_enabled',
        'display_name',
        'description',
        'configuration',
        'sort_order'
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'configuration' => 'array'
    ];

    /**
     * Get enabled payment methods
     */
    public static function getEnabledMethods()
    {
        return self::where('is_enabled', true)
                   ->orderBy('sort_order')
                   ->get();
    }

    /**
     * Check if a specific payment method is enabled
     */
    public static function isMethodEnabled($method)
    {
        return self::where('payment_method', $method)
                   ->where('is_enabled', true)
                   ->exists();
    }

    /**
     * Get all available payment methods for admin
     */
    public static function getAllMethods()
    {
        return self::orderBy('sort_order')->get();
    }
}
