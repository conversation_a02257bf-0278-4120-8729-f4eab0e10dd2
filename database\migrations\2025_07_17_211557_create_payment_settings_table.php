<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_settings', function (Blueprint $table) {
            $table->id();
            $table->string('payment_method'); // 'gcash', 'paymaya', etc.
            $table->boolean('is_enabled')->default(true);
            $table->string('display_name');
            $table->text('description')->nullable();
            $table->json('configuration')->nullable(); // Store API keys, merchant IDs, etc.
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            $table->unique('payment_method');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_settings');
    }
};
