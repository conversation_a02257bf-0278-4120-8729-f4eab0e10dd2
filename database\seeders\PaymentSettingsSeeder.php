<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PaymentSetting;

class PaymentSettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentMethods = [
            [
                'payment_method' => 'gcash',
                'is_enabled' => true,
                'display_name' => 'GCash',
                'description' => 'Pay using GCash mobile wallet',
                'configuration' => [
                    'merchant_id' => '',
                    'api_key' => '',
                    'sandbox_mode' => true
                ],
                'sort_order' => 1
            ],
            [
                'payment_method' => 'paymaya',
                'is_enabled' => true,
                'display_name' => 'PayMaya',
                'description' => 'Pay using PayMaya digital wallet',
                'configuration' => [
                    'public_key' => '',
                    'secret_key' => '',
                    'sandbox_mode' => true
                ],
                'sort_order' => 2
            ],
            [
                'payment_method' => 'cash',
                'is_enabled' => true,
                'display_name' => 'Cash Payment',
                'description' => 'Pay with cash upon service completion',
                'configuration' => [],
                'sort_order' => 99
            ]
        ];

        foreach ($paymentMethods as $method) {
            PaymentSetting::updateOrCreate(
                ['payment_method' => $method['payment_method']],
                $method
            );
        }
    }
}
