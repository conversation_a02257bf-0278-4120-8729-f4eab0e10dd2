<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="h-full dark-transition" 
    x-data="{ 
        darkMode: localStorage.getItem('darkMode') === 'true',
        pageLoading: false,
        
        async navigate(url) {
            if (this.pageLoading) return;
            this.pageLoading = true;
            
            const content = document.querySelector('.page-content');
            
            try {
                const response = await fetch(url);
                const html = await response.text();
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');

                // Update content
                const newContent = doc.querySelector('.page-content');
                if (newContent) {
                    content.innerHTML = newContent.innerHTML;
                }

                // Update title
                const newTitle = doc.querySelector('title');
                if (newTitle) {
                    document.title = newTitle.textContent;
                }

                // Update URL
                window.history.pushState({}, '', url);

                // Update navigation active states
                this.updateNavigation(url);

                // First, execute any inline scripts that might define functions
                const inlineScripts = Array.from(doc.querySelectorAll('script:not([src])'));
                inlineScripts.forEach(script => {
                    try {
                        const newScript = document.createElement('script');
                        newScript.textContent = script.textContent;
                        document.body.appendChild(newScript);
                    } catch (err) {
                        console.error('Error executing inline script:', err);
                    }
                });

                // Then handle external scripts
                const externalScripts = Array.from(doc.querySelectorAll('script[src]'));
                await Promise.all(externalScripts.map(script => {
                    return new Promise((resolve, reject) => {
                        const newScript = document.createElement('script');
                        newScript.src = script.src;
                        newScript.onload = resolve;
                        newScript.onerror = reject;
                        document.body.appendChild(newScript);
                    });
                }));

                // Wait a brief moment for scripts to be ready
                await new Promise(resolve => setTimeout(resolve, 100));

                // Reinitialize Alpine.js components
                Alpine.initTree(content);

                // Reinitialize Select2
                if (window.jQuery && jQuery().select2) {
                    jQuery('.select2').select2({
                        width: '100%',
                        dropdownParent: document.body
                    });
                }

                // Handle modals initialization
                const modals = document.querySelectorAll('[x-data]');
                modals.forEach(modal => {
                    if (modal._x_dataStack) {
                        Alpine.initTree(modal);
                    }
                });

                // Reinitialize Chart.js
                const chartCanvas = document.getElementById('dailySalesChart');
                if (chartCanvas) {
                    // Destroy existing chart if it exists
                    const existingChart = Chart.getChart(chartCanvas);
                    if (existingChart) {
                        existingChart.destroy();
                    }
                    
                    // Wait a brief moment for Chart.js to be ready
                    setTimeout(() => {
                        // If the initialization function exists, call it
                        if (typeof window.initializeDailySalesChart === 'function') {
                            window.initializeDailySalesChart();
                        } else {
                            console.warn('Daily sales chart initialization function not found');
                        }
                    }, 100);
                }

                // Execute any stacked scripts
                const stackedScripts = doc.querySelector('#script-container');
                if (stackedScripts) {
                    const scriptContent = stackedScripts.textContent.trim();
                    if (scriptContent) {
                        const newScript = document.createElement('script');
                        newScript.textContent = scriptContent;
                        document.body.appendChild(newScript);
                    }
                }
                
                // Dispatch a custom event to let page scripts know navigation is complete
                document.dispatchEvent(new CustomEvent('page:loaded'));
                
                // Reinitialize customer page event handlers
                if (url.includes('/customers')) {
                    // Wait a tiny bit to ensure all scripts have loaded
                    setTimeout(() => {
                        reinitializeCustomerEventHandlers();
                        
                        // Ensure the key modal functions are available globally
                        if (typeof openModal === 'function') window.openModal = openModal;
                        if (typeof closeModal === 'function') window.closeModal = closeModal;
                        if (typeof showModal === 'function') window.showModal = showModal;
                        if (typeof hideModal === 'function') window.hideModal = hideModal;
                        
                        console.log('Customer handler reinitialization complete');
                    }, 200);
                }
                
                // Handle repair page event handlers
                if (url.includes('/repairs')) {
                    setTimeout(() => {
                        // Try both the global function and triggering a custom event
                        if (typeof initializeRepairEventHandlers === 'function') {
                            console.log('Calling initializeRepairEventHandlers directly');
                            initializeRepairEventHandlers();
                        }
                        
                        // Always dispatch the page:loaded event for event-based initialization
                        document.dispatchEvent(new CustomEvent('page:loaded', {
                            detail: { url: url, section: 'repairs' }
                        }));
                        
                        console.log('Repair handler reinitialization complete');
                    }, 200);
                }

            } catch (error) {
                console.error('Navigation error:', error);
                window.location.href = url;
            }

            this.pageLoading = false;
        },

        updateNavigation(url) {
            // Update sidebar navigation active states
            document.querySelectorAll('.sidebar-item a, .mobile-sidebar a').forEach(a => {
                if (a.getAttribute('href') === url) {
                    // Remove inactive classes
                    a.classList.remove('text-gray-700', 'dark:text-gray-300', 'hover:bg-gray-100', 'dark:hover:bg-gray-700');
                    // Add active classes
                    a.classList.add('bg-blue-100', 'dark:bg-blue-900', 'text-blue-700', 'dark:text-blue-200', 'nav-item-active');
                    // Update icon color
                    const icon = a.querySelector('svg');
                    if (icon) {
                        icon.classList.remove('text-gray-500', 'group-hover:text-gray-700', 'dark:group-hover:text-gray-300');
                        icon.classList.add('text-blue-500');
                    }
                } else {
                    // Remove active classes
                    a.classList.remove('bg-blue-100', 'dark:bg-blue-900', 'text-blue-700', 'dark:text-blue-200', 'nav-item-active');
                    // Add inactive classes
                    a.classList.add('text-gray-700', 'dark:text-gray-300', 'hover:bg-gray-100', 'dark:hover:bg-gray-700');
                    // Update icon color
                    const icon = a.querySelector('svg');
                    if (icon) {
                        icon.classList.remove('text-blue-500');
                        icon.classList.add('text-gray-500', 'group-hover:text-gray-700', 'dark:group-hover:text-gray-300');
                    }
                }
            });
        },

        mobileMenuOpen: false,
        toggleMenu() {
            this.mobileMenuOpen = !this.mobileMenuOpen;
            document.body.classList.toggle('menu-open', this.mobileMenuOpen);
        }
    }" 
    x-init="
        $watch('darkMode', val => localStorage.setItem('darkMode', val));
        
        // Handle navigation without page refresh
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (link && link.href && link.href.startsWith(window.location.origin) && 
                !link.hasAttribute('download') && 
                !link.href.includes('/profile') && // Skip SPA navigation for profile pages
                !link.classList.contains('no-spa')) {
                e.preventDefault();
                navigate(link.href);
            }
        });

        // Handle browser back/forward
        window.addEventListener('popstate', () => {
            navigate(window.location.href);
        });

        // Handle form submissions
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.method.toLowerCase() === 'get') {
                e.preventDefault();
                const formData = new FormData(form);
                const queryString = new URLSearchParams(formData).toString();
                const url = form.action + (queryString ? '?' + queryString : '');
                navigate(url);
            }
        });
    "
    :class="{ 'dark': darkMode }">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>VSMART SMS</title>

        <!-- Favicon -->
        <link rel="icon" href="{{ asset('img/LogoClear.png') }}" type="image/png">

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.bunny.net">
        <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

        <!-- Scripts -->
        @vite(['resources/css/app.css', 'resources/js/app.js'])
        <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
        
        <style>
            /* Dark mode transition */
            .dark-transition,
            .dark-transition * {
                transition: background-color 0.5s ease,
                            border-color 0.5s ease,
                            color 0.5s ease !important;
            }

            /* Hide elements with x-cloak */
            [x-cloak] {
                display: none !important;
            }

            /* Preserve button colors during transition */
            .dark-transition button.bg-blue-600 {
                transition: transform 0.3s ease,
                            opacity 0.3s ease !important;
            }

            /* Loading indicator */
            .loading-bar {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                height: 2px;
                background: linear-gradient(to right, #3b82f6, #60a5fa);
                transform-origin: 0;
                z-index: 50;
                opacity: 0.7;
            }

            /* Dark mode toggle animation */
            .theme-toggle-icon {
                transform-origin: center;
            }

            /* Page content */
            .page-content {
                position: relative;
                width: 100%;
            }

            /* Ensure content doesn't overflow */
            body {
                overflow-x: hidden;
            }

            /* Sidebar styles */
            .sidebar-transition {
                transition: width 0.3s ease-in-out;
            }

            /* Tooltip for collapsed sidebar */
            .sidebar-tooltip {
                position: absolute;
                left: 100%;
                top: 50%;
                transform: translateY(-50%);
                margin-left: 0.5rem;
                padding: 0.25rem 0.5rem;
                background-color: rgba(0, 0, 0, 0.8);
                color: white;
                border-radius: 0.25rem;
                font-size: 0.75rem;
                white-space: nowrap;
                opacity: 0;
                pointer-events: none;
                transition: opacity 0.2s ease-in-out;
                z-index: 1000;
            }

            .sidebar-item:hover .sidebar-tooltip {
                opacity: 1;
            }

            /* Smooth scrollbar for sidebar */
            .sidebar-scroll::-webkit-scrollbar {
                width: 4px;
            }

            .sidebar-scroll::-webkit-scrollbar-track {
                background: transparent;
            }

            .sidebar-scroll::-webkit-scrollbar-thumb {
                background: rgba(156, 163, 175, 0.5);
                border-radius: 2px;
            }

            .sidebar-scroll::-webkit-scrollbar-thumb:hover {
                background: rgba(156, 163, 175, 0.7);
            }

            /* Mobile menu animation improvements */
            @media (max-width: 1023px) {
                .mobile-sidebar-overlay {
                    backdrop-filter: blur(4px);
                }
            }

            /* Active navigation item glow effect */
            .nav-item-active {
                box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.3);
            }

            /* Hover effects for navigation items */
            .nav-item:hover {
                transform: translateX(2px);
            }

            .nav-item-collapsed:hover {
                transform: scale(1.05);
            }
        </style>
        @stack('styles')
    </head>
    <body class="font-sans antialiased h-full bg-gray-100 dark:bg-gray-900">
        <!-- Loading bar -->
        <div x-show="pageLoading" 
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"
             class="loading-bar"></div>

        <!-- Sidebar Layout -->
        <div class="min-h-screen bg-gray-100 dark:bg-gray-900"
            x-data="{
                sidebarOpen: false,
                sidebarCollapsed: localStorage.getItem('sidebarCollapsed') === 'true',
                toggleSidebar() {
                    this.sidebarOpen = !this.sidebarOpen;
                },
                toggleCollapse() {
                    this.sidebarCollapsed = !this.sidebarCollapsed;
                    localStorage.setItem('sidebarCollapsed', this.sidebarCollapsed);
                },
                closeSidebar() {
                    this.sidebarOpen = false;
                }
            }">

            <!-- Desktop Sidebar -->
            <div class="hidden lg:fixed lg:inset-y-0 lg:flex lg:flex-col"
                 :class="sidebarCollapsed ? 'lg:w-16' : 'lg:w-64'">
                <!-- Sidebar component -->
                <div class="flex flex-col flex-grow bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 shadow-sm">
                    <!-- Logo and collapse button -->
                    <div class="flex items-center justify-between px-4 py-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center" :class="sidebarCollapsed ? 'justify-center' : ''">
                            <a href="{{ route('dashboard') }}" class="flex items-center">
                                <div class="h-8 w-8 bg-red-600 rounded-lg flex items-center justify-center">
                                    <span class="text-white font-bold text-lg">V</span>
                                </div>
                                <span x-show="!sidebarCollapsed"
                                      x-transition:enter="transition ease-out duration-200"
                                      x-transition:enter-start="opacity-0 transform scale-95"
                                      x-transition:enter-end="opacity-100 transform scale-100"
                                      x-transition:leave="transition ease-in duration-150"
                                      x-transition:leave-start="opacity-100 transform scale-100"
                                      x-transition:leave-end="opacity-0 transform scale-95"
                                      class="ml-2 text-xl font-bold text-gray-900 dark:text-white">
                                    SMART
                                </span>
                            </a>
                        </div>
                        <button @click="toggleCollapse()"
                                x-show="!sidebarCollapsed"
                                x-transition
                                class="p-1.5 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7"/>
                            </svg>
                        </button>
                    </div>

                    <!-- Navigation -->
                    <nav class="flex-1 px-2 py-4 space-y-1 sidebar-scroll overflow-y-auto">
                        <!-- Dashboard -->
                        <div class="relative sidebar-item">
                            <a href="{{ route('dashboard') }}"
                               class="nav-item group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 {{ request()->routeIs('dashboard') ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200 nav-item-active' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700' }}"
                               :class="sidebarCollapsed ? 'justify-center nav-item-collapsed' : ''">
                                <svg class="flex-shrink-0 h-5 w-5 {{ request()->routeIs('dashboard') ? 'text-blue-500' : 'text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300' }}"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"/>
                                </svg>
                                <span x-show="!sidebarCollapsed"
                                      x-transition:enter="transition ease-out duration-200 delay-100"
                                      x-transition:enter-start="opacity-0"
                                      x-transition:enter-end="opacity-100"
                                      x-transition:leave="transition ease-in duration-150"
                                      x-transition:leave-start="opacity-100"
                                      x-transition:leave-end="opacity-0"
                                      class="ml-3">Dashboard</span>
                            </a>
                            <div x-show="sidebarCollapsed" class="sidebar-tooltip">Dashboard</div>
                        </div>

                        <!-- Services -->
                        <div class="relative sidebar-item">
                            <a href="{{ route('services.index') }}"
                               class="nav-item group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 {{ request()->routeIs('services.*') ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200 nav-item-active' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700' }}"
                               :class="sidebarCollapsed ? 'justify-center nav-item-collapsed' : ''">
                                <svg class="flex-shrink-0 h-5 w-5 {{ request()->routeIs('services.*') ? 'text-blue-500' : 'text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300' }}"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                                <span x-show="!sidebarCollapsed"
                                      x-transition:enter="transition ease-out duration-200 delay-100"
                                      x-transition:enter-start="opacity-0"
                                      x-transition:enter-end="opacity-100"
                                      x-transition:leave="transition ease-in duration-150"
                                      x-transition:leave-start="opacity-100"
                                      x-transition:leave-end="opacity-0"
                                      class="ml-3">Services</span>
                            </a>
                            <div x-show="sidebarCollapsed" class="sidebar-tooltip">Services</div>
                        </div>

                        <!-- Customers -->
                        <div class="relative sidebar-item">
                            <a href="{{ route('customers.index') }}"
                               class="nav-item group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 {{ request()->routeIs('customers.*') ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200 nav-item-active' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700' }}"
                               :class="sidebarCollapsed ? 'justify-center nav-item-collapsed' : ''">
                                <svg class="flex-shrink-0 h-5 w-5 {{ request()->routeIs('customers.*') ? 'text-blue-500' : 'text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300' }}"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                                </svg>
                                <span x-show="!sidebarCollapsed"
                                      x-transition:enter="transition ease-out duration-200 delay-100"
                                      x-transition:enter-start="opacity-0"
                                      x-transition:enter-end="opacity-100"
                                      x-transition:leave="transition ease-in duration-150"
                                      x-transition:leave-start="opacity-100"
                                      x-transition:leave-end="opacity-0"
                                      class="ml-3">Customers</span>
                            </a>
                            <div x-show="sidebarCollapsed" class="sidebar-tooltip">Customers</div>
                        </div>

                        <!-- Repairs -->
                        <div class="relative sidebar-item">
                            <a href="{{ route('repairs.index') }}"
                               class="nav-item group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 {{ request()->routeIs('repairs.*') ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200 nav-item-active' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700' }}"
                               :class="sidebarCollapsed ? 'justify-center nav-item-collapsed' : ''">
                                <svg class="flex-shrink-0 h-5 w-5 {{ request()->routeIs('repairs.*') ? 'text-blue-500' : 'text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300' }}"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                                <span x-show="!sidebarCollapsed"
                                      x-transition:enter="transition ease-out duration-200 delay-100"
                                      x-transition:enter-start="opacity-0"
                                      x-transition:enter-end="opacity-100"
                                      x-transition:leave="transition ease-in duration-150"
                                      x-transition:leave-start="opacity-100"
                                      x-transition:leave-end="opacity-0"
                                      class="ml-3">Repairs</span>
                            </a>
                            <div x-show="sidebarCollapsed" class="sidebar-tooltip">Repairs</div>
                        </div>

                        <!-- Feedbacks -->
                        <div class="relative sidebar-item">
                            <a href="{{ route('feedback.index') }}"
                               class="nav-item group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 {{ request()->routeIs('feedback.*') ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200 nav-item-active' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700' }}"
                               :class="sidebarCollapsed ? 'justify-center nav-item-collapsed' : ''">
                                <svg class="flex-shrink-0 h-5 w-5 {{ request()->routeIs('feedback.*') ? 'text-blue-500' : 'text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300' }}"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                                </svg>
                                <span x-show="!sidebarCollapsed"
                                      x-transition:enter="transition ease-out duration-200 delay-100"
                                      x-transition:enter-start="opacity-0"
                                      x-transition:enter-end="opacity-100"
                                      x-transition:leave="transition ease-in duration-150"
                                      x-transition:leave-start="opacity-100"
                                      x-transition:leave-end="opacity-0"
                                      class="ml-3">Feedbacks</span>
                            </a>
                            <div x-show="sidebarCollapsed" class="sidebar-tooltip">Feedbacks</div>
                        </div>

                        <!-- Inventory -->
                        <div class="relative sidebar-item">
                            <a href="{{ route('inventory.index') }}"
                               class="nav-item group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 {{ request()->routeIs('inventory.*') ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200 nav-item-active' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700' }}"
                               :class="sidebarCollapsed ? 'justify-center nav-item-collapsed' : ''">
                                <svg class="flex-shrink-0 h-5 w-5 {{ request()->routeIs('inventory.*') ? 'text-blue-500' : 'text-gray-500 group-hover:text-gray-700 dark:group-hover:text-gray-300' }}"
                                     fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                                </svg>
                                <span x-show="!sidebarCollapsed"
                                      x-transition:enter="transition ease-out duration-200 delay-100"
                                      x-transition:enter-start="opacity-0"
                                      x-transition:enter-end="opacity-100"
                                      x-transition:leave="transition ease-in duration-150"
                                      x-transition:leave-start="opacity-100"
                                      x-transition:leave-end="opacity-0"
                                      class="ml-3">Inventory</span>
                            </a>
                            <div x-show="sidebarCollapsed" class="sidebar-tooltip">Inventory</div>
                        </div>
                    </nav>

                    <!-- Expand button when collapsed -->
                    <div x-show="sidebarCollapsed" class="p-2">
                        <button @click="toggleCollapse()"
                                class="w-full p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <svg class="h-5 w-5 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Mobile sidebar overlay -->
            <div x-show="sidebarOpen"
                 x-transition:enter="transition-opacity ease-linear duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="transition-opacity ease-linear duration-300"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 class="fixed inset-0 z-40 lg:hidden">
                <div class="fixed inset-0 bg-gray-600 bg-opacity-75" @click="closeSidebar()"></div>
            </div>

            <!-- Mobile sidebar -->
            <div x-show="sidebarOpen"
                 x-transition:enter="transition ease-in-out duration-300 transform"
                 x-transition:enter-start="-translate-x-full"
                 x-transition:enter-end="translate-x-0"
                 x-transition:leave="transition ease-in-out duration-300 transform"
                 x-transition:leave-start="translate-x-0"
                 x-transition:leave-end="-translate-x-full"
                 class="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 lg:hidden">
                <div class="flex flex-col h-full">
                    <!-- Mobile header -->
                    <div class="flex items-center justify-between px-4 py-4 border-b border-gray-200 dark:border-gray-700">
                        <div class="flex items-center">
                            <div class="h-8 w-8 bg-red-600 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-lg">V</span>
                            </div>
                            <span class="ml-2 text-xl font-bold text-gray-900 dark:text-white">SMART</span>
                        </div>
                        <button @click="closeSidebar()"
                                class="p-1.5 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </button>
                    </div>

                    <!-- Mobile navigation -->
                    <nav class="flex-1 px-2 py-4 space-y-1 mobile-sidebar">
                        <a href="{{ route('dashboard') }}" @click="closeSidebar()"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('dashboard') ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700' }}">
                            <svg class="flex-shrink-0 h-5 w-5 mr-3 {{ request()->routeIs('dashboard') ? 'text-blue-500' : 'text-gray-500' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"/>
                            </svg>
                            Dashboard
                        </a>

                        <a href="{{ route('services.index') }}" @click="closeSidebar()"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('services.*') ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700' }}">
                            <svg class="flex-shrink-0 h-5 w-5 mr-3 {{ request()->routeIs('services.*') ? 'text-blue-500' : 'text-gray-500' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            Services
                        </a>

                        <a href="{{ route('customers.index') }}" @click="closeSidebar()"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('customers.*') ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700' }}">
                            <svg class="flex-shrink-0 h-5 w-5 mr-3 {{ request()->routeIs('customers.*') ? 'text-blue-500' : 'text-gray-500' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                            </svg>
                            Customers
                        </a>

                        <a href="{{ route('repairs.index') }}" @click="closeSidebar()"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('repairs.*') ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700' }}">
                            <svg class="flex-shrink-0 h-5 w-5 mr-3 {{ request()->routeIs('repairs.*') ? 'text-blue-500' : 'text-gray-500' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                            Repairs
                        </a>

                        <a href="{{ route('feedback.index') }}" @click="closeSidebar()"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('feedback.*') ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700' }}">
                            <svg class="flex-shrink-0 h-5 w-5 mr-3 {{ request()->routeIs('feedback.*') ? 'text-blue-500' : 'text-gray-500' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                            </svg>
                            Feedbacks
                        </a>

                        <a href="{{ route('inventory.index') }}" @click="closeSidebar()"
                           class="group flex items-center px-2 py-2 text-sm font-medium rounded-md {{ request()->routeIs('inventory.*') ? 'bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700' }}">
                            <svg class="flex-shrink-0 h-5 w-5 mr-3 {{ request()->routeIs('inventory.*') ? 'text-blue-500' : 'text-gray-500' }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
                            </svg>
                            Inventory
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Top bar for mobile and user controls -->
            <div class="flex-1" :class="sidebarCollapsed ? 'lg:pl-16' : 'lg:pl-64'">
                <div class="sticky top-0 z-30 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
                    <div class="flex items-center justify-between px-4 py-3">
                        <!-- Mobile menu button -->
                        <button @click="toggleSidebar()"
                                class="lg:hidden p-2 rounded-md text-gray-500 hover:text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                            </svg>
                        </button>

                        <!-- Page title or breadcrumb could go here -->
                        <div class="flex-1"></div>

                        <!-- User controls -->
                        <div class="flex items-center space-x-3">
                            @include('layouts.navigation')
                        </div>
                    </div>
                </div>

                <!-- Page Content -->
                <main class="py-6">
                    <div class="page-content max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                        @yield('content')
                    </div>
                </main>
            </div>
        </div>

        @stack('scripts')
        <div id="script-container" style="display: none;">
            @stack('scripts')
        </div>
        
        <script>
            // Create Alpine.js store for dark mode
            document.addEventListener('alpine:init', () => {
                Alpine.store('darkMode', {
                    dark: localStorage.getItem('darkMode') === 'true',
                    
                    toggle() {
                        this.dark = !this.dark;
                        localStorage.setItem('darkMode', this.dark);
                        
                        if (this.dark) {
                            document.documentElement.classList.add('dark');
                            document.getElementById('darkModeText').textContent = 'Light Mode';
                            document.getElementById('darkModeTextMobile').textContent = 'Light Mode';
                            document.querySelector('.dark-mode-light')?.classList.add('hidden');
                            document.querySelector('.dark-mode-dark')?.classList.remove('hidden');
                        } else {
                            document.documentElement.classList.remove('dark');
                            document.getElementById('darkModeText').textContent = 'Dark Mode';
                            document.getElementById('darkModeTextMobile').textContent = 'Dark Mode';
                            document.querySelector('.dark-mode-light')?.classList.remove('hidden');
                            document.querySelector('.dark-mode-dark')?.classList.add('hidden');
                        }
                    },
                    
                    init() {
                        if (this.dark) {
                            document.documentElement.classList.add('dark');
                        } else {
                            document.documentElement.classList.remove('dark');
                        }
                    }
                });
            });

            // Function to reinitialize all customer page event handlers
            function reinitializeCustomerEventHandlers() {
                console.log('Reinitializing customer event handlers');
                
                // Make sure the modal functions are available
                if (typeof showModal === 'function') {
                    window.showModal = showModal;
                }
                
                if (typeof hideModal === 'function') {
                    window.hideModal = hideModal;
                }
                
                if (typeof openModal === 'function') {
                    window.openModal = openModal;
                    
                    // Add New Customer button
                    const addNewCustomerBtn = document.querySelector('button[onclick="openModal()"]');
                    if (addNewCustomerBtn) {
                        addNewCustomerBtn.onclick = function() {
                            openModal();
                        };
                    }
                }
                
                // View customer buttons
                const viewButtons = document.querySelectorAll('button[onclick^="openViewModal"]');
                viewButtons.forEach(button => {
                    const customerId = button.getAttribute('data-customer-id');
                    button.onclick = function() {
                        if (typeof openViewModal === 'function') {
                            openViewModal(customerId);
                        } else {
                            console.error('openViewModal function not found');
                        }
                    };
                });
                
                // Other buttons with onclick handlers
                document.querySelectorAll('button[onclick]').forEach(button => {
                    const onclickValue = button.getAttribute('onclick');
                    if (onclickValue && !button.customHandlerAttached) {
                        const handlerName = onclickValue.split('(')[0];
                        const params = onclickValue.match(/\((.*)\)/);
                        
                        button.customHandlerAttached = true;
                        button.addEventListener('click', function(e) {
                            e.preventDefault();
                            
                            if (typeof window[handlerName] === 'function') {
                                // For buttons that use dataset attributes
                                if (onclickValue.includes('this.dataset')) {
                                    const customerId = this.getAttribute('data-customer-id');
                                    window[handlerName](customerId);
                                } else {
                                    // Try to execute the original function
                                    try {
                                        // Extract parameters and execute safely
                                        const paramValue = params ? params[1].replace(/['"]/g, '') : '';
                                        if (paramValue) {
                                            window[handlerName](paramValue);
                                        } else {
                                            window[handlerName]();
                                        }
                                    } catch (err) {
                                        console.error('Error executing handler:', err);
                                    }
                                }
                            }
                        });
                    }
                });
                
                // Forms with custom handlers
                const deleteForms = document.querySelectorAll('.delete-customer-form');
                deleteForms.forEach(form => {
                    if (!form.hasSubmitListener) {
                        form.hasSubmitListener = true;
                        form.addEventListener('submit', function(e) {
                            e.preventDefault();
                            if (confirm('Are you sure you want to delete this customer?')) {
                                form.submit();
                            }
                        });
                    }
                });
            }

            // Call when page first loads
            document.addEventListener('DOMContentLoaded', function() {
                if (window.location.pathname.includes('/customers')) {
                    reinitializeCustomerEventHandlers();
                }
            });

            // Also reinitialize on SPA navigation completion
            document.addEventListener('page:loaded', function() {
                if (window.location.pathname.includes('/customers')) {
                    reinitializeCustomerEventHandlers();
                }
            });

            // Function to reinitialize repair page event handlers
            function reinitializeRepairEventHandlers() {
                // Check if there are repair-specific functions that were defined
                if (typeof window.initializeRepairEventHandlers === 'function') {
                    console.log('Found initializeRepairEventHandlers in window object, calling it');
                    window.initializeRepairEventHandlers();
                } else {
                    console.log('initializeRepairEventHandlers not found in window object');
                    
                    // Dispatch a custom event that repair pages can listen for
                    document.dispatchEvent(new CustomEvent('repair:reinitialize'));
                    
                    // Check if we're on a repair page by looking for repair elements
                    const paymentModal = document.getElementById('payment-modal');
                    const paymentButtons = document.querySelectorAll('.payment-button');
                    
                    if (paymentModal || paymentButtons.length > 0) {
                        console.log('Repair elements found but no handler function, adding page reload fallback');
                        // If elements exist but no handler, we may need to reload the page
                        const reloadNeeded = !window.repairHandlersInitialized;
                        
                        if (reloadNeeded) {
                            console.log('Reloading page to reinitialize repair handlers');
                            window.location.reload();
                        }
                    }
                }
                
                // Mark that we've attempted initialization
                window.repairHandlersInitialized = true;
            }

            // Ensure Alpine.js is properly initialized
            document.addEventListener('DOMContentLoaded', function() {
                // Initialize Alpine.js if needed
                if (typeof Alpine !== 'undefined' && !window.alpineInitialized) {
                    Alpine.start();
                    window.alpineInitialized = true;
                    
                    // Force Alpine.js to reinitialize all components
                    document.querySelectorAll('[x-data]').forEach(el => {
                        if (el._x_dataStack) {
                            el._x_dataStack.forEach(item => {
                                if (typeof item === 'object') {
                                    // Ensure all Alpine.js states are properly reset
                                    Object.keys(item).forEach(key => {
                                        if (key.startsWith('is') && typeof item[key] === 'boolean') {
                                            item[key] = false;
                                        }
                                    });
                                }
                            });
                        }
                    });
                }
            });

            // Listen for navigation events and reinitialize Alpine components
            document.addEventListener('turbo:load', function() {
                if (typeof Alpine !== 'undefined') {
                    Alpine.start();
                }
            });

            // Notifications handling
            document.addEventListener('DOMContentLoaded', function() {
                // Initial load of notification counts
                fetchNotificationCounts();
                
                // Set up interval to refresh notification counts every 15 seconds
                setInterval(fetchNotificationCounts, 15000);
                
                // Add event listener for the mark all as read link
                document.addEventListener('click', function(e) {
                    if (e.target.matches('.mark-all-read') || e.target.closest('.mark-all-read')) {
                        e.preventDefault();
                        markAllNotificationsAsRead();
                    }
                });
                
                // Force an immediate refresh of notification count when page loads
                setTimeout(fetchNotificationCounts, 1000);
            });
            
            function fetchNotificationCounts() {
                fetch('/notifications/counts')
                    .then(response => response.json())
                    .then(data => {
                        console.log('Fetched notification counts:', data);
                        
                        // Calculate the total if needed (though it should be provided by the server now)
                        const totalCount = data.total_count || 
                                         (data.unread_count + 
                                          data.pending_repairs_count + 
                                          data.in_progress_repairs_count + 
                                          data.unpaid_repairs_count);
                        
                        // Dispatch a custom event that the notification bell can listen for
                        window.dispatchEvent(new CustomEvent('notification-count-updated', {
                            detail: { count: totalCount }
                        }));
                        
                        // Update notification count directly in Alpine component
                        const bellComponent = document.getElementById('notification-bell');
                        if (bellComponent && bellComponent.__x) {
                            bellComponent.__x.$data.unreadCount = totalCount;
                            
                            // Make sure the badge is visible when count > 0
                            const badge = bellComponent.querySelector('.notification-badge');
                            if (badge) {
                                if (totalCount > 0) {
                                    badge.style.display = 'flex';
                                    badge.textContent = totalCount > 99 ? '99+' : totalCount;
                                } else {
                                    badge.style.display = 'none';
                                }
                            }
                        }
                    })
                    .catch(error => console.error('Error fetching notification counts:', error));
            }
            
            function loadNotifications() {
                fetch('/notifications/data')
                    .then(response => response.json())
                    .then(data => {
                        // Handle system notifications
                        const systemNotificationsContainer = document.getElementById('system-notifications');
                        systemNotificationsContainer.innerHTML = '';
                        
                        if (data.notifications.length === 0) {
                            systemNotificationsContainer.innerHTML = `
                                <div class="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400">
                                    No new notifications
                                </div>
                            `;
                        } else {
                            data.notifications.forEach(notification => {
                                const element = createNotificationElement(notification);
                                systemNotificationsContainer.appendChild(element);
                            });
                        }
                        
                        // Handle pending repairs
                        const pendingRepairsContainer = document.getElementById('pending-repairs');
                        if (pendingRepairsContainer) {
                            pendingRepairsContainer.innerHTML = '';
                            
                            if (data.pending_repairs.length === 0) {
                                pendingRepairsContainer.innerHTML = `
                                    <div class="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400">
                                        No pending repairs
                                    </div>
                                `;
                            } else {
                                data.pending_repairs.forEach(repair => {
                                    const element = createRepairNotificationElement(repair, 'pending');
                                    pendingRepairsContainer.appendChild(element);
                                });
                            }
                        }
                        
                        // Handle in-progress repairs
                        const inProgressRepairsContainer = document.getElementById('in-progress-repairs');
                        if (inProgressRepairsContainer) {
                            inProgressRepairsContainer.innerHTML = '';
                            
                            if (data.in_progress_repairs.length === 0) {
                                inProgressRepairsContainer.innerHTML = `
                                    <div class="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400">
                                        No in-progress repairs
                                    </div>
                                `;
                            } else {
                                data.in_progress_repairs.forEach(repair => {
                                    const element = createRepairNotificationElement(repair, 'in_progress');
                                    inProgressRepairsContainer.appendChild(element);
                                });
                            }
                        }
                        
                        // Handle unpaid repairs
                        const unpaidRepairsContainer = document.getElementById('unpaid-repairs');
                        unpaidRepairsContainer.innerHTML = '';
                        
                        if (data.unpaid_repairs.length === 0) {
                            unpaidRepairsContainer.innerHTML = `
                                <div class="px-4 py-3 text-center text-sm text-gray-500 dark:text-gray-400">
                                    No unpaid repairs
                                </div>
                            `;
                        } else {
                            data.unpaid_repairs.forEach(repair => {
                                const element = createRepairNotificationElement(repair, 'unpaid');
                                unpaidRepairsContainer.appendChild(element);
                            });
                        }
                        
                        // Update count
                        const totalCount = data.notifications.length + 
                                          data.pending_repairs.length + 
                                          data.in_progress_repairs.length +
                                          data.unpaid_repairs.length;
                        
                        const countBadge = document.getElementById('notification-count');
                        if (totalCount > 0) {
                            countBadge.textContent = totalCount > 99 ? '99+' : totalCount;
                            countBadge.classList.remove('hidden');
                        } else {
                            countBadge.classList.add('hidden');
                        }
                        
                        // Show or hide "no notifications" message
                        const noNotificationsMsg = document.getElementById('no-notifications');
                        if (totalCount > 0) {
                            noNotificationsMsg.classList.add('hidden');
                        } else {
                            noNotificationsMsg.classList.remove('hidden');
                        }
                    })
                    .catch(error => console.error('Error fetching notifications:', error));
            }
            
            function createNotificationElement(notification) {
                const div = document.createElement('div');
                div.className = 'px-4 py-3 border-b border-gray-100 dark:border-gray-700 ' + 
                                (notification.read_at ? 'bg-white dark:bg-gray-800' : 'bg-blue-50 dark:bg-blue-900');
                
                const data = notification.data;
                
                // Create content based on notification type
                if (notification.type.includes('NewFeedbackNotification')) {
                    if (data.type === 'unfeatured_feedback') {
                        // Unfeatured feedback notification
                        div.innerHTML = `
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3">
                                    <svg class="h-5 w-5 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">Unfeatured Feedback Reminder</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        You have ${data.count || 0} unfeatured feedback${(data.count || 0) > 1 ? 's' : ''} waiting for review
                                    </p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <a href="/feedback" class="text-blue-600 dark:text-blue-400 hover:underline">
                                            Review Feedback
                                        </a>
                                    </p>
                                </div>
                            </div>
                        `;
                    } else if (data.type === 'sales_report_reminder') {
                        // Sales report reminder notification
                        div.innerHTML = `
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3">
                                    <svg class="h-5 w-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 3a1 1 0 000 2h10a1 1 0 100-2H3zm0 4a1 1 0 000 2h10a1 1 0 100-2H3zm0 4a1 1 0 100 2h10a1 1 0 100-2H3zm12-7a1 1 0 10-2 0v6a1 1 0 102 0V4z" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">${data.period === 'weekly' ? 'Weekly' : 'Monthly'} Sales Report</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        ${data.message || 'Sales report is now available'}
                                    </p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <a href="${data.report_url || '/reports/generate'}" class="text-blue-600 dark:text-blue-400 hover:underline">
                                            View Report
                                        </a>
                                    </p>
                                </div>
                            </div>
                        `;
                    } else if (data.type === 'shared_feedback') {
                        // Shared feedback notification
                        div.innerHTML = `
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3">
                                    <svg class="h-5 w-5 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">Shared Feedback</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        ${data.shared_by || 'Someone'} shared feedback from ${data.name || 'a customer'} (${data.rating || '?'}/5)
                                    </p>
                                    ${data.shared_message ? `
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <strong>Note:</strong> "${data.shared_message}"
                                    </p>
                                    ` : ''}
                                    ${data.message ? `
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <strong>Feedback:</strong> "${data.message.substring(0, 100)}${data.message.length > 100 ? '...' : ''}"
                                    </p>
                                    ` : ''}
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        <a href="/feedback/${data.id}/share" class="text-blue-600 dark:text-blue-400 hover:underline">
                                            View Full Feedback
                                        </a>
                                    </p>
                                </div>
                            </div>
                        `;
                    } else {
                        // Regular feedback notification
                        div.innerHTML = `
                            <div class="flex items-start">
                                <div class="flex-shrink-0 mr-3">
                                    <svg class="h-5 w-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">New Feedback</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        ${data.name || 'Customer'} left a ${data.rating || '?'}/5 rating
                                    </p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">${new Date(data.created_at).toLocaleString()}</p>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    // Default notification
                    div.innerHTML = `
                        <div class="flex items-start">
                            <div class="flex-shrink-0 mr-3">
                                <svg class="h-5 w-5 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-gray-900 dark:text-white">System Notification</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    You have a new notification
                                </p>
                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">${new Date(notification.created_at).toLocaleString()}</p>
                            </div>
                        </div>
                    `;
                }
                
                return div;
            }
            
            function createRepairNotificationElement(repair, status) {
                const div = document.createElement('div');
                div.className = 'px-4 py-3 border-b border-gray-100 dark:border-gray-700 ' + 
                                (status === 'pending' ? 'bg-yellow-50 dark:bg-yellow-900/20' : 
                                 status === 'in_progress' ? 'bg-blue-50 dark:bg-blue-900/20' : 
                                 status === 'unpaid' ? 'bg-red-50 dark:bg-red-900/20' : 'bg-gray-50 dark:bg-gray-900/20');
                
                const statusText = status === 'pending' ? 'Pending Repair' : 
                                   status === 'in_progress' ? 'In Progress Repair' : 
                                   status === 'unpaid' ? 'Unpaid Repair' : 'Repair';
                
                const iconClass = status === 'pending' ? 'text-yellow-500' : 
                                  status === 'in_progress' ? 'text-blue-500' : 
                                  status === 'unpaid' ? 'text-red-500' : 'text-gray-500';
                
                let iconPath;
                
                if (status === 'pending') {
                    iconPath = '<path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 110-12 6 6 0 010 12zm-1-5a1 1 0 011-1h2a1 1 0 110 2h-2a1 1 0 01-1-1zm1-7a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd" />';
                } else if (status === 'in_progress') {
                    iconPath = '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd" />';
                } else if (status === 'unpaid') {
                    iconPath = '<path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v4a2 2 0 002 2V6h10a2 2 0 00-2-2H4zm2 6a2 2 0 012-2h8a2 2 0 012 2v4a2 2 0 01-2 2H8a2 2 0 01-2-2v-4zm6 4a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd" />';
                } else {
                    iconPath = '<path fill-rule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 110-12 6 6 0 010 12zm-1-5a1 1 0 011-1h2a1 1 0 110 2h-2a1 1 0 01-1-1zm1-7a1 1 0 100 2 1 1 0 000-2z" clip-rule="evenodd" />';
                }
                
                // Get the first item details
                const firstItem = repair.items[0];
                const deviceName = firstItem ? `${firstItem.device.brand} ${firstItem.device.model}` : 'Unknown device';
                const customerName = firstItem && firstItem.device.customer ? firstItem.device.customer.name : 'Unknown customer';
                
                // Additional info for unpaid repairs
                let additionalInfo = '';
                if (status === 'unpaid' && repair.total_cost) {
                    additionalInfo = `<span class="text-red-600 dark:text-red-400 font-medium"> • ₱${parseFloat(repair.total_cost).toFixed(2)}</span>`;
                }
                
                div.innerHTML = `
                    <div class="flex items-start">
                        <div class="flex-shrink-0 mr-2">
                            <svg class="h-4 w-4 ${iconClass}" fill="currentColor" viewBox="0 0 20 20">
                                ${iconPath}
                            </svg>
                        </div>
                        <div class="flex-grow text-xs">
                            <p class="text-gray-900 dark:text-white font-medium">
                                ${statusText}${additionalInfo}
                            </p>
                            <p class="text-gray-500 dark:text-gray-400 truncate">
                                ${deviceName} for ${customerName}
                            </p>
                            <p class="text-gray-500 dark:text-gray-400 mt-1">
                                <a href="/repairs/${repair.id}" class="text-blue-600 dark:text-blue-400 hover:underline">
                                    View details
                                </a>
                            </p>
                        </div>
                    </div>
                `;
                
                return div;
            }
            
            function markAllNotificationsAsRead() {
                fetch('/notifications/mark-all-read', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Marked all notifications as read:', data);
                    
                    // Refresh notification counts
                    fetchNotificationCounts();
                    
                    // Refresh notification content if the dropdown is open
                    const bellComponent = document.getElementById('notification-bell');
                    if (bellComponent && bellComponent.__x && bellComponent.__x.$data.open) {
                        loadNotifications();
                    }
                    
                    // If we're on the notifications page, dispatch a custom event
                    if (window.location.pathname.includes('/notifications')) {
                        window.dispatchEvent(new CustomEvent('notificationBellClicked'));
                    }
                })
                .catch(error => console.error('Error marking notifications as read:', error));
            }
        </script>

        <!-- Add backdrop overlay -->
        <div 
            x-show="mobileMenuOpen" 
            x-transition:enter="transition ease-out duration-200"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-150"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            @click="mobileMenuOpen = false"
            class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-40 sm:hidden"
        ></div>
    </body>
</html>
