@extends('layouts.app')

@section('content')
<style>
    /* Hide sidebar and header on QR settings page */
    .lg\\:fixed.lg\\:inset-y-0.lg\\:flex.lg\\:flex-col,
    .fixed.inset-y-0.left-0.z-50.w-64,
    header.sticky.top-0.z-40 {
        display: none !important;
    }

    /* Adjust main content area */
    .flex-1 {
        padding-left: 0 !important;
    }

    .qr-settings-container {
        padding-top: 2rem !important;
        min-height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .qr-settings-content {
        background: white;
        border-radius: 1rem;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .return-button {
        transition: all 0.3s ease;
    }

    .return-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
    }

    /* Ensure full width */
    main {
        margin-left: 0 !important;
        max-width: none !important;
    }

    .page-content {
        max-width: none !important;
        padding: 0 !important;
    }
</style>

<div class="page-content">
    <div class="qr-settings-container py-8">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Return to Dashboard Button -->
            <div class="mb-6">
                <a href="{{ route('dashboard') }}"
                   class="return-button inline-flex items-center px-6 py-3 bg-white text-gray-700 font-medium rounded-lg shadow-md hover:bg-gray-50 transition-all duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    Return to Dashboard
                </a>
            </div>

            <div class="qr-settings-content p-8">
                <!-- Header -->
                <div class="mb-8 text-center">
                    <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h4M4 8h4m0 0V4m0 4h4m0 0V4m0 4v4m0 0h4m-4 0v4" />
                        </svg>
                    </div>
                    <h1 class="text-4xl font-bold text-gray-900 mb-2">QR Payment Settings</h1>
                    <p class="text-lg text-gray-600">Control which QR payment methods are available to customers on the guest dashboard</p>
                </div>

                <!-- QR Payment Methods -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- GCash QR -->
                    <div class="bg-white rounded-lg shadow-lg border border-gray-200 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">GCash QR Code</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Show GCash QR payment option to guests</p>
                        </div>
                    </div>
                    
                    <!-- Enable/Disable Button -->
                    <div class="flex items-center">
                        <button type="button"
                                class="qr-toggle-btn px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 {{ $gcashEnabled ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-green-600 hover:bg-green-700 text-white' }}"
                                data-method="gcash"
                                data-current-state="{{ $gcashEnabled ? 'true' : 'false' }}">
                            {{ $gcashEnabled ? 'Disable' : 'Enable' }}
                        </button>
                    </div>
                </div>

                <!-- Status -->
                <div class="mb-4">
                    <span class="gcash-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $gcashEnabled ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                        {{ $gcashEnabled ? 'QR Code Visible' : 'QR Code Hidden' }}
                    </span>
                </div>

                <div class="text-sm text-gray-600 dark:text-gray-400">
                    <p>When enabled, customers will see the GCash QR code option in the payment section of the guest dashboard.</p>
                </div>
            </div>

            <!-- PayMaya QR -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3.5 6L12 10.5 8.5 8 12 5.5 15.5 8zM12 13.5L8.5 16 12 18.5 15.5 16 12 13.5z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">PayMaya QR Code</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Show PayMaya QR payment option to guests</p>
                        </div>
                    </div>
                    
                    <!-- Enable/Disable Button -->
                    <div class="flex items-center">
                        <button type="button"
                                class="qr-toggle-btn px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 {{ $paymayaEnabled ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-green-600 hover:bg-green-700 text-white' }}"
                                data-method="paymaya"
                                data-current-state="{{ $paymayaEnabled ? 'true' : 'false' }}">
                            {{ $paymayaEnabled ? 'Disable' : 'Enable' }}
                        </button>
                    </div>
                </div>

                <!-- Status -->
                <div class="mb-4">
                    <span class="paymaya-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $paymayaEnabled ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' }}">
                        {{ $paymayaEnabled ? 'QR Code Visible' : 'QR Code Hidden' }}
                    </span>
                </div>

                <div class="text-sm text-gray-600 dark:text-gray-400">
                    <p>When enabled, customers will see the PayMaya QR code option in the payment section of the guest dashboard.</p>
                </div>
            </div>
        </div>

        <!-- Warning Alert -->
        <div id="warning-alert" class="mt-6 {{ (!$gcashEnabled && !$paymayaEnabled) ? '' : 'hidden' }}">
            <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                            Warning: All QR Payment Methods Disabled
                        </h3>
                        <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                            <p>When all QR payment methods are disabled, customers will only see cash payment instructions on the guest dashboard.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Section -->
        <div class="mt-8 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        How QR Payment Settings Work
                    </h3>
                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                        <ul class="list-disc list-inside space-y-1">
                            <li>These settings control which QR payment tabs are shown to customers on the guest dashboard</li>
                            <li>Customers can only see and use QR codes for enabled payment methods</li>
                            <li>Disabled QR codes will be completely hidden from the guest interface</li>
                            <li>Changes take effect immediately for all new customer visits</li>
                        </ul>
                    </div>
                </div>
            </div>
            </div>
        </div>
    </div>
</div>
</div>

@push('scripts')
<script>
// Function to initialize QR settings functionality
function initializeQrSettings() {
    console.log('Initializing QR Settings');

    // Remove existing event listeners to prevent duplicates
    document.querySelectorAll('.qr-toggle-btn').forEach(button => {
        button.replaceWith(button.cloneNode(true));
    });

    // Handle QR method toggle
    document.querySelectorAll('.qr-toggle-btn').forEach(button => {
        console.log('Found toggle button for:', button.dataset.method);

        button.addEventListener('click', function() {
            const method = this.dataset.method;
            const currentState = this.dataset.currentState === 'true';
            const newState = !currentState; // Toggle the state

            console.log('Button clicked:', method, 'current:', currentState, 'new:', newState);

            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]');
            if (!csrfToken) {
                console.error('CSRF token not found');
                showNotification('CSRF token not found', 'error');
                this.checked = !isEnabled;
                return;
            }

            fetch('/qr-settings/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken.getAttribute('content')
                },
                body: JSON.stringify({
                    method: method,
                    enabled: newState
                })
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);

                if (data.success) {
                    // Update status badge
                    const statusElement = document.querySelector(`.${method}-status`);

                    if (newState) {
                        statusElement.className = `${method}-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200`;
                        statusElement.textContent = 'QR Code Visible';
                    } else {
                        statusElement.className = `${method}-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200`;
                        statusElement.textContent = 'QR Code Hidden';
                    }

                    // Update button appearance and state
                    this.dataset.currentState = newState.toString();
                    if (newState) {
                        // Now enabled - show disable button (red)
                        this.className = 'qr-toggle-btn px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 bg-red-600 hover:bg-red-700 text-white';
                        this.textContent = 'Disable';
                    } else {
                        // Now disabled - show enable button (green)
                        this.className = 'qr-toggle-btn px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 bg-green-600 hover:bg-green-700 text-white';
                        this.textContent = 'Enable';
                    }

                    // Check if warning should be shown
                    checkQrStatus();

                    // Show success message
                    showNotification(data.message, 'success');
                } else {
                    // Don't need to revert anything for buttons
                    showNotification(data.message || 'Failed to update QR setting', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('An error occurred while updating QR setting', 'error');
            });
        });
    });

    // Initial check
    checkQrStatus();

    function checkQrStatus() {
        const gcashButton = document.querySelector('[data-method="gcash"]');
        const paymayaButton = document.querySelector('[data-method="paymaya"]');
        const warningAlert = document.getElementById('warning-alert');

        if (!gcashButton || !paymayaButton || !warningAlert) {
            console.error('Required elements not found');
            return;
        }

        const gcashEnabled = gcashButton.dataset.currentState === 'true';
        const paymayaEnabled = paymayaButton.dataset.currentState === 'true';
        const anyEnabled = gcashEnabled || paymayaEnabled;

        if (!anyEnabled) {
            warningAlert.classList.remove('hidden');
        } else {
            warningAlert.classList.add('hidden');
        }
    }
    
    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg transition-all duration-300 ${
            type === 'success' 
                ? 'bg-green-100 border border-green-400 text-green-700' 
                : 'bg-red-100 border border-red-400 text-red-700'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize on DOM ready
document.addEventListener('DOMContentLoaded', initializeQrSettings);

// Initialize on SPA navigation
document.addEventListener('page:loaded', function() {
    if (document.querySelector('.qr-toggle-btn')) {
        console.log('Reinitializing QR settings after SPA navigation');
        initializeQrSettings();
    }
});

// Initialize immediately if elements are already present (for direct page loads)
if (document.querySelector('.qr-toggle-btn')) {
    initializeQrSettings();
}
</script>
@endpush
