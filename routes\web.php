<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ServiceController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\DeviceController;
use App\Http\Controllers\RepairController;
use App\Http\Controllers\InventoryController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\Auth\PasswordController;
use App\Http\Controllers\FeedbackController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\GuestController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\PaymentSettingsController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/

// Landing page route (accessible to everyone)
Route::get('/', function () {
    return view('welcome');
})->name('home');

// About Us page route
Route::get('/about-us', function () {
    return view('about-us');
})->name('about-us');

// Guest routes for repair tracking
Route::get('/guest/login', [GuestController::class, 'showLoginForm'])->name('guest.login');
Route::post('/guest/login', [GuestController::class, 'login'])->name('guest.login.submit');
Route::get('/guest/dashboard', [GuestController::class, 'dashboard'])->name('guest.dashboard');
Route::post('/guest/logout', [GuestController::class, 'logout'])->name('guest.logout');
Route::get('/guest/repair-status', [GuestController::class, 'getRepairStatus'])->name('guest.repair-status');

// Dashboard route (requires authentication)
Route::get('/dashboard', [DashboardController::class, 'index'])
    ->middleware(['auth'])
    ->name('dashboard');

Route::middleware('auth')->group(function () {
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile.show');
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    Route::put('/profile/password', [PasswordController::class, 'update'])->name('password.update');

    // Resource routes
    Route::resource('categories', CategoryController::class);
    Route::resource('services', ServiceController::class);
    Route::resource('customers', CustomerController::class);
    Route::resource('devices', DeviceController::class)->only(['store', 'update', 'destroy']);
    Route::resource('repairs', RepairController::class)->middleware(['auth', 'verified']);
    Route::resource('inventory', InventoryController::class);

    Route::get('/repairs/{repair}/receipt', [RepairController::class, 'receipt'])->name('repairs.receipt')->middleware(['auth', 'verified']);
    Route::post('/repairs/{repair}/toggle-payment', [RepairController::class, 'togglePaymentStatus'])->name('repairs.toggle-payment')->middleware(['auth', 'verified']);
    Route::post('/repairs/{repair}/mark-as-paid', [RepairController::class, 'markAsPaid'])->name('repairs.markAsPaid')->middleware(['auth', 'verified']);

    // Report routes
    Route::get('/reports', [ReportController::class, 'generate'])->name('reports.generate');

    // Sales data route for AJAX requests
    Route::get('/dashboard/sales-data', [DashboardController::class, 'getSalesData'])
        ->name('dashboard.sales-data');

    // Add this with your other routes
    Route::get('/api/customers/{customer}/devices', [CustomerController::class, 'devices'])
        ->name('customers.devices');
        
    // Add device to customer
    Route::post('/customers/{customer}/devices', [CustomerController::class, 'addDevice'])
        ->name('customers.devices.store')
        ->middleware('auth');
        
    // API route for services
    Route::get('/api/services', [ServiceController::class, 'index'])
        ->name('api.services')
        ->middleware('auth');

    // Device routes
    Route::get('/devices/{device}/edit', [DeviceController::class, 'edit'])->name('devices.edit');
    Route::delete('/devices/{device}', [DeviceController::class, 'destroy'])->name('devices.destroy');
    Route::put('/devices/{device}', [DeviceController::class, 'update'])->name('devices.update');

    // Feedback routes
    Route::get('/feedback', [FeedbackController::class, 'index'])->name('feedback.index');
    Route::patch('/feedback/{feedback}/toggle-featured', [FeedbackController::class, 'toggleFeatured'])->name('feedback.toggle-featured');
    Route::delete('/feedback/{feedback}', [FeedbackController::class, 'destroy'])->name('feedback.destroy');
    Route::get('/feedback/{feedback}', [FeedbackController::class, 'show'])->name('feedback.show');
    Route::get('/feedback/{feedback}/share', [FeedbackController::class, 'share'])->name('feedback.share');
    
    // Notification routes
    Route::get('/notifications/counts', [NotificationController::class, 'getNotificationCounts'])->name('notifications.counts');
    Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index');
    Route::get('/notifications/data', [NotificationController::class, 'getNotifications'])->name('notifications.data');
    Route::post('/notifications/mark-all-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.mark-all-read');
    Route::post('/notifications/mark-read', [NotificationController::class, 'markAsRead'])->name('notifications.mark-read');
    
    // User Management routes
    Route::get('/users/verify', [UserController::class, 'verifyAccess'])->name('users.verify');
    Route::post('/users/verify', [UserController::class, 'processVerification'])->name('users.process-verification');
    Route::post('/users/update-password', [UserController::class, 'updateVerificationPassword'])->name('users.update-verification-password');
    Route::get('/users', [UserController::class, 'index'])->name('users.index');
    Route::post('/users', [UserController::class, 'store'])->name('users.store');
    Route::patch('/users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');
    Route::delete('/users/{user}', [UserController::class, 'destroy'])->name('users.destroy');
    Route::post('/users/{user}/reset-password', [UserController::class, 'resetPassword'])->name('users.reset-password');
    Route::patch('/users/{user}/update-position', [UserController::class, 'updatePosition'])->name('users.update-position');
});

// Public routes for feedback submission
Route::post('/feedback', [FeedbackController::class, 'store'])
    ->name('feedback.store');

// Secure admin password reset route (keep this URL secret - only you should know it)
Route::get('/system-maintenance/admin-reset/{key}', function($key) {
    // A unique, hard-to-guess key that only you know
    $secretKey = 'VSmart_SecureKey_928374';
    
    if ($key !== $secretKey) {
        abort(404); // Hide the route completely if key doesn't match
    }
    
    $hash = \Illuminate\Support\Facades\Hash::make('admin123');
    
    try {
        $setting = \App\Models\Setting::where('key', 'admin_password')->first();
        if ($setting) {
            $setting->value = $hash;
            $setting->save();
            return "Admin password has been reset to 'admin123' using proper bcrypt hash.";
        } else {
            \App\Models\Setting::create([
                'key' => 'admin_password',
                'value' => $hash,
            ]);
            return "Admin password has been created as 'admin123' using proper bcrypt hash.";
        }
    } catch (\Exception $e) {
        return "Error: " . $e->getMessage();
    }
});

// Payment Settings Routes
Route::middleware('auth')->group(function () {
    Route::get('/payment-settings', [PaymentSettingsController::class, 'index'])->name('payment-settings.index');
    Route::post('/payment-settings/{id}/status', [PaymentSettingsController::class, 'updateStatus'])->name('payment-settings.update-status');
    Route::post('/payment-settings/{id}/configuration', [PaymentSettingsController::class, 'updateConfiguration'])->name('payment-settings.update-configuration');
    Route::get('/api/payment-methods/enabled', [PaymentSettingsController::class, 'getEnabledMethods'])->name('api.payment-methods.enabled');
});

require __DIR__.'/auth.php';

