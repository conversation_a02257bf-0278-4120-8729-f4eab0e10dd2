<?php $__env->startSection('content'); ?>
<div class="py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">QR Payment Settings</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">Control which QR payment methods are available to customers on the guest dashboard</p>
        </div>

        <!-- QR Payment Methods -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- GCash QR -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">GCash QR Code</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Show GCash QR payment option to guests</p>
                        </div>
                    </div>
                    
                    <!-- Toggle Switch -->
                    <div class="flex items-center">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   class="sr-only peer qr-toggle" 
                                   data-method="gcash"
                                   <?php echo e($gcashEnabled ? 'checked' : ''); ?>>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>

                <!-- Status -->
                <div class="mb-4">
                    <span class="gcash-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($gcashEnabled ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'); ?>">
                        <?php echo e($gcashEnabled ? 'QR Code Visible' : 'QR Code Hidden'); ?>

                    </span>
                </div>

                <div class="text-sm text-gray-600 dark:text-gray-400">
                    <p>When enabled, customers will see the GCash QR code option in the payment section of the guest dashboard.</p>
                </div>
            </div>

            <!-- PayMaya QR -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                            <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3.5 6L12 10.5 8.5 8 12 5.5 15.5 8zM12 13.5L8.5 16 12 18.5 15.5 16 12 13.5z"/>
                            </svg>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">PayMaya QR Code</h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Show PayMaya QR payment option to guests</p>
                        </div>
                    </div>
                    
                    <!-- Toggle Switch -->
                    <div class="flex items-center">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   class="sr-only peer qr-toggle" 
                                   data-method="paymaya"
                                   <?php echo e($paymayaEnabled ? 'checked' : ''); ?>>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"></div>
                        </label>
                    </div>
                </div>

                <!-- Status -->
                <div class="mb-4">
                    <span class="paymaya-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($paymayaEnabled ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'); ?>">
                        <?php echo e($paymayaEnabled ? 'QR Code Visible' : 'QR Code Hidden'); ?>

                    </span>
                </div>

                <div class="text-sm text-gray-600 dark:text-gray-400">
                    <p>When enabled, customers will see the PayMaya QR code option in the payment section of the guest dashboard.</p>
                </div>
            </div>
        </div>

        <!-- Warning Alert -->
        <div id="warning-alert" class="mt-6 <?php echo e((!$gcashEnabled && !$paymayaEnabled) ? '' : 'hidden'); ?>">
            <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                            Warning: All QR Payment Methods Disabled
                        </h3>
                        <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                            <p>When all QR payment methods are disabled, customers will only see cash payment instructions on the guest dashboard.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Section -->
        <div class="mt-8 bg-blue-50 dark:bg-blue-900 border border-blue-200 dark:border-blue-700 rounded-md p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800 dark:text-blue-200">
                        How QR Payment Settings Work
                    </h3>
                    <div class="mt-2 text-sm text-blue-700 dark:text-blue-300">
                        <ul class="list-disc list-inside space-y-1">
                            <li>These settings control which QR payment tabs are shown to customers on the guest dashboard</li>
                            <li>Customers can only see and use QR codes for enabled payment methods</li>
                            <li>Disabled QR codes will be completely hidden from the guest interface</li>
                            <li>Changes take effect immediately for all new customer visits</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle QR method toggle
    document.querySelectorAll('.qr-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            const method = this.dataset.method;
            const isEnabled = this.checked;
            
            fetch('/qr-settings/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    method: method,
                    enabled: isEnabled
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update status badge
                    const statusElement = document.querySelector(`.${method}-status`);
                    
                    if (isEnabled) {
                        statusElement.className = `${method}-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200`;
                        statusElement.textContent = 'QR Code Visible';
                    } else {
                        statusElement.className = `${method}-status inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200`;
                        statusElement.textContent = 'QR Code Hidden';
                    }
                    
                    // Check if warning should be shown
                    checkQrStatus();
                    
                    // Show success message
                    showNotification(data.message, 'success');
                } else {
                    // Revert toggle if failed
                    this.checked = !isEnabled;
                    showNotification('Failed to update QR setting', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.checked = !isEnabled;
                showNotification('An error occurred while updating QR setting', 'error');
            });
        });
    });
    
    function checkQrStatus() {
        const gcashToggle = document.querySelector('[data-method="gcash"]');
        const paymayaToggle = document.querySelector('[data-method="paymaya"]');
        const warningAlert = document.getElementById('warning-alert');
        
        const anyEnabled = gcashToggle.checked || paymayaToggle.checked;
        
        if (!anyEnabled) {
            warningAlert.classList.remove('hidden');
        } else {
            warningAlert.classList.add('hidden');
        }
    }
    
    function showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-md shadow-lg transition-all duration-300 ${
            type === 'success' 
                ? 'bg-green-100 border border-green-400 text-green-700' 
                : 'bg-red-100 border border-red-400 text-red-700'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Vsmart System\vsmart sms main\resources\views/qr-settings/index.blade.php ENDPATH**/ ?>