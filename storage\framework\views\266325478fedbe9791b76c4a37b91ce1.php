<?php $__env->startSection('content'); ?>
<div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
    <div class="p-6 text-gray-900 dark:text-gray-100">
        <!-- Header Section with Actions -->
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div class="flex items-center space-x-3">
                <div class="bg-blue-100 dark:bg-blue-900 p-2 rounded-lg">
                    <svg class="h-8 w-8 text-blue-600 dark:text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2M7 7h10"></path>
                    </svg>
                </div>
                <div>
                    <h1 class="text-2xl font-bold">Repair #<?php echo e($repair->id); ?></h1>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Created <?php echo e($repair->created_at->setTimezone('Asia/Manila')->format('F j, Y g:i A')); ?> PHT</p>
                    <!-- Payment Status Badge -->
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1
                        <?php if($repair->payment_status === 'paid'): ?> 
                            bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                        <?php else: ?> 
                            bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300
                        <?php endif; ?>">
                        <?php echo e($repair->payment_status === 'paid' ? 'Paid' : 'Unpaid'); ?>

                    </span>
                </div>
            </div>
            <div class="flex flex-wrap items-center gap-3">
                <!-- Button Layout Design - always in same position -->
                <div class="flex flex-wrap gap-3">
                    <!-- Edit button - always in position 1 (hidden if completed) -->
                    <div class="<?php echo e($repair->status !== 'completed' ? '' : 'hidden'); ?>">
                        <a href="<?php echo e(route('repairs.edit', $repair)); ?>" 
                            class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 btn-hover-effect">
                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                            Edit Repair
                        </a>
                    </div>
                    
                    <!-- Receipt button - always in position 2 (hidden if not completed) -->
                    <div class="<?php echo e($repair->status === 'completed' ? '' : 'hidden'); ?>">
                        <button type="button"
                            class="inline-flex items-center px-4 py-2 bg-amber-600 text-white rounded-md hover:bg-amber-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-amber-500 transition-all duration-200 btn-hover-effect"
                            onclick="window.open('<?php echo e(route('repairs.receipt', $repair)); ?>', '_blank');">
                            <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                            </svg>
                            View Receipt
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <?php if(session('success')): ?>
            <div class="mb-6 p-4 bg-green-100 dark:bg-green-900 border-l-4 border-green-500 rounded-md flex items-center justify-between animate__animated animate__fadeIn">
                <div class="flex items-center">
                    <svg class="h-5 w-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <span class="text-green-700 dark:text-green-300"><?php echo e(session('success')); ?></span>
                </div>
            </div>
        <?php endif; ?>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Left Column: Status and Customer Info -->
            <div class="lg:col-span-1 space-y-6">
                <!-- Status Card -->
                <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm overflow-hidden border border-gray-200 dark:border-gray-600">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Status Information</h3>
                        <div class="space-y-4">
                            <div>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium
                                    <?php if($repair->status === 'completed'): ?> bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300
                                    <?php elseif($repair->status === 'pending'): ?> bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300
                                    <?php elseif($repair->status === 'in_progress'): ?> bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300
                                    <?php elseif($repair->status === 'cancelled'): ?> bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300
                                    <?php endif; ?>">
                                    <?php if($repair->status === 'completed'): ?>
                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        Completed
                                    <?php elseif($repair->status === 'pending'): ?>
                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        Pending
                                    <?php elseif($repair->status === 'in_progress'): ?>
                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        In Progress
                                    <?php elseif($repair->status === 'cancelled'): ?>
                                        <svg class="w-4 h-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                        </svg>
                                        Cancelled
                                    <?php endif; ?>
                                </span>
                            </div>
                            
                            <div class="border-t dark:border-gray-600 pt-4 space-y-3">
                                <!-- Date Received -->
                                <div class="flex items-center text-sm">
                                    <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                    <span class="text-gray-600 dark:text-gray-300">
                                        Received: <?php echo e($repair->created_at->timezone('Asia/Manila')->format('F j, Y g:i A')); ?> PHT
                                    </span>
                                </div>
                                
                                <?php if($repair->started_at && $repair->status !== 'pending'): ?>
                                    <div class="flex items-center text-sm">
                                        <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                        </svg>
                                        <span class="text-gray-600 dark:text-gray-300">Started: <?php echo e($repair->started_at->timezone('Asia/Manila')->format('F j, Y g:i A')); ?> PHT</span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if($repair->completed_at): ?>
                                    <div class="flex items-center text-sm">
                                        <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                        </svg>
                                        <span class="text-gray-600 dark:text-gray-300">Completed: <?php echo e($repair->completed_at->timezone('Asia/Manila')->format('F j, Y g:i A')); ?> PHT</span>
                                    </div>
                                <?php endif; ?>
                                
                                <?php if($repair->started_at && $repair->completed_at && $repair->status !== 'pending'): ?>
                                    <div class="flex items-center text-sm">
                                        <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                        </svg>
                                        <span class="text-gray-600 dark:text-gray-300">
                                            Duration: 
                                            <?php
                                                $duration = $repair->started_at->diff($repair->completed_at);
                                                $parts = [];
                                                
                                                if ($duration->d > 0) {
                                                    $parts[] = $duration->d . ' ' . Str::plural('day', $duration->d);
                                                }
                                                if ($duration->h > 0) {
                                                    $parts[] = $duration->h . ' ' . Str::plural('hour', $duration->h);
                                                }
                                                if ($duration->i > 0) {
                                                    $parts[] = $duration->i . ' ' . Str::plural('minute', $duration->i);
                                                }
                                                
                                                echo empty($parts) ? 'Less than a minute' : implode(', ', $parts);
                                            ?>
                                        </span>
                                    </div>
                                <?php endif; ?>

                                <!-- <?php if($repair->payment_method): ?>
                                    <div class="flex items-center text-sm">
                                        <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                                        </svg>
                                        <span class="text-gray-600 dark:text-gray-300">
                                            Payment Method: <?php echo e($repair->payment_method === 'gcash/maya' ? 'GCash/Maya' : ucfirst($repair->payment_method)); ?>

                                        </span>
                                    </div>
                                <?php endif; ?> -->

                                <!-- Tracking Key -->
                                <div class="flex items-center text-sm">
                                    <svg class="h-5 w-5 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                                    </svg>
                                    <span class="text-gray-600 dark:text-gray-300">
                                        Tracking Key: <span class="font-mono bg-gray-100 dark:bg-gray-600 px-2 py-0.5 rounded text-blue-700 dark:text-blue-300"><?php echo e($repair->special_key ?? 'N/A'); ?></span>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Customer Info Card -->
                <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm overflow-hidden border border-gray-200 dark:border-gray-600">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Customer Information</h3>
                        <div class="space-y-4">
                            <?php if($repair->items->isNotEmpty()): ?>
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-12 w-12 rounded-full customer-avatar avatar-<?php echo e(strtolower(substr($repair->items->first()->device->customer->name, 0, 1))); ?> flex items-center justify-center text-xl font-bold">
                                        <?php echo e(strtoupper(substr($repair->items->first()->device->customer->name, 0, 1))); ?>

                                    </div>
                                    <div class="ml-4">
                                        <a href="<?php echo e(route('customers.show', $repair->items->first()->device->customer)); ?>" 
                                           class="text-lg font-medium text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                                            <?php echo e($repair->items->first()->device->customer->name); ?>

                                        </a>
                                        <div class="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-1">
                                            <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                            </svg>
                                            <?php echo e($repair->items->first()->device->customer->phone); ?>

                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Notes Card -->
                <?php if($repair->notes): ?>
                    <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm overflow-hidden border border-gray-200 dark:border-gray-600">
                        <div class="px-4 py-5 sm:p-6">
                            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Additional Notes</h3>
                            <p class="text-gray-600 dark:text-gray-300 text-sm whitespace-pre-wrap"><?php echo e($repair->notes); ?></p>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Payment Status -->
                <div class="mt-6 space-y-2">
                    <!-- <h3 class="text-lg font-medium text-gray-900">Payment Status</h3> -->
                    <div class="bg-white dark:bg-gray-700 shadow-sm rounded-lg overflow-hidden border border-gray-200 dark:border-gray-600 <?php echo e($repair->status === 'cancelled' ? 'opacity-60' : ''); ?>">
                        <div class="px-4 py-5 sm:p-6">
                            <div class="sm:flex sm:items-start sm:justify-between">
                                <div>
                                    <h3 class="text-base font-medium text-gray-900 dark:text-white">
                                        <?php echo e($repair->payment_status === 'paid' ? 'Payment completed' : 'Payment pending'); ?>

                                    </h3>
                                    <div class="mt-2 max-w-xl text-sm text-gray-500 dark:text-gray-400">
                                        <p>
                                            <?php echo e($repair->payment_status === 'paid' 
                                                ? 'This repair has been fully paid for.'
                                                : ($repair->status === 'cancelled' 
                                                   ? 'This repair was cancelled.'
                                                   : 'This repair has not been paid for yet.')); ?>

                                        </p>
                                    </div>
                                    
                                    <?php if($repair->payment_status === 'paid'): ?>
                                    <div class="mt-3 space-y-2 bg-gray-50 dark:bg-gray-600 p-3 rounded">
                                        <div class="flex items-center justify-between text-sm">
                                            <span class="text-gray-600 dark:text-gray-300">Payment Method:</span>
                                            <span class="font-medium px-2 py-1 rounded <?php echo e($repair->payment_method === 'cash'
                                                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                                                    : ($repair->payment_method === 'gcash/maya'
                                                        ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
                                                        : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300'
                                                    )); ?>">
                                                <?php echo e($repair->payment_method === 'gcash/maya' ? 'GCash/Maya' : ucfirst($repair->payment_method)); ?>

                                            </span>
                                        </div>
                                        
                                        <div class="flex items-center justify-between text-sm">
                                            <span class="text-gray-600 dark:text-gray-300">Amount Paid:</span>
                                            <span class="font-bold text-gray-900 dark:text-white">₱<?php echo e(number_format($repair->total_cost, 2)); ?></span>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="mt-5 sm:mt-0 sm:ml-6 sm:flex-shrink-0 sm:flex sm:items-center">
                                    <?php if($repair->payment_status === 'paid'): ?>
                                        <span class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 dark:bg-green-500">
                                            <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                            </svg>
                                            Paid
                                        </span>
                                    <?php else: ?>
                                        <button type="button" class="<?php echo e($repair->status === 'cancelled' ? 'cancelled-payment-button' : 'payment-button'); ?> inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 dark:bg-indigo-500 dark:hover:bg-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800">
                                            <svg class="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"/>
                                            </svg>
                                            Mark as Paid
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column: Repair Items -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-700 rounded-lg shadow-sm overflow-hidden border border-gray-200 dark:border-gray-600">
                    <div class="px-4 py-5 sm:p-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-6">Repair Items</h3>
                        <div class="space-y-6">
                            <?php $__currentLoopData = $repair->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="<?php if(!$loop->last): ?> border-b border-gray-200 dark:border-gray-600 pb-6 <?php endif; ?>">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <!-- Device Details -->
                                        <div class="flex items-start space-x-4">
                                            <div class="flex-shrink-0">
                                                <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                                                    <svg class="h-6 w-6 text-blue-600 dark:text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div>
                                                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                                                    <?php if($item->device->deviceModel): ?>
                                                        <?php echo e($item->device->deviceModel->full_name); ?>

                                                    <?php else: ?>
                                                        <?php echo e($item->device->brand); ?> <?php echo e($item->device->model); ?>

                                                    <?php endif; ?>
                                                </h4>
                                                <?php if($item->device->serial_number): ?>
                                                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                                        Serial Number: <?php echo e($item->device->serial_number); ?>

                                                    </p>
                                                <?php endif; ?>
                                            </div>
                                        </div>

                                        <!-- Service Details -->
                                        <div class="flex items-start space-x-4">
                                            <div class="flex-shrink-0">
                                                <div class="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                                                    <svg class="h-6 w-6 text-purple-600 dark:text-purple-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div>
                                                <h4 class="text-lg font-medium text-gray-900 dark:text-white"><?php echo e($item->service->name); ?></h4>
                                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Cost: ₱<?php echo e(number_format($item->cost, 2)); ?></p>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if($item->notes): ?>
                                        <div class="mt-4 bg-gray-50 dark:bg-gray-600 rounded-md p-4">
                                            <p class="text-sm text-gray-600 dark:text-gray-300"><?php echo e($item->notes); ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>

                        <!-- Total Cost -->
                        <div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-600">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-medium text-gray-900 dark:text-white">Total Cost</span>
                                <span class="text-2xl font-bold text-gray-900 dark:text-white">₱<?php echo e(number_format($repair->total_cost, 2)); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Back Button -->
        <div class="mt-6">
            <a href="<?php echo e(route('repairs.index')); ?>" 
               onclick="window.location.href=this.href; return false;"
               class="inline-flex items-center text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                <svg class="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                Back to Repairs
            </a>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
    .btn-hover-effect {
        transition: all 0.2s ease;
    }
    
    .btn-hover-effect:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .customer-avatar {
        background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
        box-shadow: 0 2px 10px rgba(99, 102, 241, 0.2);
        transition: all 0.3s ease;
    }

    .dark .customer-avatar {
        background: linear-gradient(135deg, #4f46e5 0%, #9333ea 100%);
        box-shadow: 0 2px 10px rgba(79, 70, 229, 0.3);
    }

    /* Enhanced avatar with colored variants based on first letter */
    .avatar-a, .avatar-j, .avatar-s { background: linear-gradient(135deg, #3b82f6, #2dd4bf); }
    .avatar-b, .avatar-k, .avatar-t { background: linear-gradient(135deg, #8b5cf6, #ec4899); }
    .avatar-c, .avatar-l, .avatar-u { background: linear-gradient(135deg, #f59e0b, #ef4444); }
    .avatar-d, .avatar-m, .avatar-v { background: linear-gradient(135deg, #10b981, #3b82f6); }
    .avatar-e, .avatar-n, .avatar-w { background: linear-gradient(135deg, #6366f1, #8b5cf6); }
    .avatar-f, .avatar-o, .avatar-x { background: linear-gradient(135deg, #f97316, #f59e0b); }
    .avatar-g, .avatar-p, .avatar-y { background: linear-gradient(135deg, #ec4899, #f97316); }
    .avatar-h, .avatar-q, .avatar-z { background: linear-gradient(135deg, #14b8a6, #6366f1); }
    .avatar-i, .avatar-r, .avatar-0 { background: linear-gradient(135deg, #ef4444, #f59e0b); }

    .dark .avatar-a, .dark .avatar-j, .dark .avatar-s { background: linear-gradient(135deg, #2563eb, #0d9488); }
    .dark .avatar-b, .dark .avatar-k, .dark .avatar-t { background: linear-gradient(135deg, #7c3aed, #db2777); }
    .dark .avatar-c, .dark .avatar-l, .dark .avatar-u { background: linear-gradient(135deg, #d97706, #dc2626); }
    .dark .avatar-d, .dark .avatar-m, .dark .avatar-v { background: linear-gradient(135deg, #059669, #2563eb); }
    .dark .avatar-e, .dark .avatar-n, .dark .avatar-w { background: linear-gradient(135deg, #4f46e5, #7c3aed); }
    .dark .avatar-f, .dark .avatar-o, .dark .avatar-x { background: linear-gradient(135deg, #ea580c, #d97706); }
    .dark .avatar-g, .dark .avatar-p, .dark .avatar-y { background: linear-gradient(135deg, #db2777, #ea580c); }
    .dark .avatar-h, .dark .avatar-q, .dark .avatar-z { background: linear-gradient(135deg, #0d9488, #4f46e5); }
    .dark .avatar-i, .dark .avatar-r, .dark .avatar-0 { background: linear-gradient(135deg, #dc2626, #d97706); }

    /* Fix for the check mark appearing full screen */
    #payment-status-modal.hidden {
        display: none !important;
    }
    
    #payment-status-modal.hidden * {
        display: none !important;
    }
    
    #payment-modal-icon svg {
        max-width: 24px;
        max-height: 24px;
    }
    
    /* Hide scrollbar but allow scrolling */
    .scrollbar-hide {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;     /* Firefox */
        max-height: 80vh;          /* Maximum height on mobile */
    }
    
    /* Hide scrollbar for Chrome, Safari and Opera */
    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<!-- Modal for confirming payment -->
<div id="payment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50" style="display: none !important;">
    <div class="bg-white rounded-lg max-w-md w-full mx-4 shadow-xl overflow-hidden">
        <!-- Modal header -->
        <div class="bg-gray-50 px-4 py-3 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-medium text-gray-900">Confirm Payment</h3>
            <button type="button" class="payment-modal-close text-gray-400 hover:text-gray-500 focus:outline-none">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        
        <!-- Modal content -->
        <div class="px-4 py-5 overflow-y-auto scrollbar-hide">
            <div class="text-center mb-4">
                <div id="payment-modal-icon" class="mx-auto p-2 rounded-full bg-green-100 inline-block">
                    <svg class="h-8 w-8 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mt-2">Confirm Payment Receipt</h3>
                <p class="text-sm text-gray-500 mt-1">
                    You're about to mark this repair as paid. This action cannot be undone.
                </p>
            </div>
            
            <div class="bg-gray-50 p-3 rounded-md mb-4">
                <div class="flex justify-between items-center">
                    <span class="text-sm font-medium text-gray-700">Total Amount</span>
                    <span class="text-base font-bold text-gray-900">₱<?php echo e(number_format($repair->total_cost, 2)); ?></span>
                </div>
            </div>
            
            <form id="payment-form" action="<?php echo e(route('repairs.markAsPaid', $repair)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="mb-4">
                    <label for="payment_method" class="block text-sm font-medium text-gray-700 mb-1">Payment Method</label>
                    <select id="payment_method" name="payment_method" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <option value="cash">Cash</option>
                        <option value="gcash/maya">GCash/Maya</option>
                        <option value="bank">Bank Transfer</option>
                    </select>
                </div>
                
                <div class="flex justify-end space-x-3 mt-5">
                    <button type="button" class="payment-modal-close inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Cancel
                    </button>
                    <button type="submit" class="inline-flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Confirm Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal for cancelled repair payment -->
<div id="cancelled-payment-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden flex items-center justify-center z-50" style="display: none !important;">
    <div class="bg-white rounded-lg max-w-md w-full mx-4 shadow-xl overflow-hidden">
        <!-- Modal header -->
        <div class="bg-gray-50 px-4 py-3 border-b border-gray-200 flex justify-between items-center">
            <h3 class="text-lg font-medium text-gray-900">Service Cancelled</h3>
            <button type="button" class="cancelled-modal-close text-gray-400 hover:text-gray-500 focus:outline-none">
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        
        <!-- Modal content -->
        <div class="px-4 py-5 overflow-y-auto scrollbar-hide">
            <div class="text-center mb-4">
                <div class="mx-auto p-2 rounded-full bg-red-100 inline-block">
                    <svg class="h-8 w-8 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mt-2">Cannot Process Payment</h3>
                <p class="text-sm text-gray-500 mt-1">
                    This service has been cancelled and cannot proceed to payment.
                </p>
            </div>
            
            <div class="flex justify-end space-x-3 mt-5">
                <button type="button" class="cancelled-modal-close inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    // Initialize when the page is first loaded
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Repairs page loaded - initializing event handlers');
        initializeRepairEventHandlers();
    });
    
    // Additionally listen for SPA navigation completion events from global app navigation
    document.addEventListener('page:loaded', function() {
        console.log('SPA navigation detected - reinitializing repair event handlers');
        initializeRepairEventHandlers();
    });
    
    // Also listen for Livewire navigation events as a fallback
    document.addEventListener('livewire:navigated', function() {
        console.log('Livewire navigation detected - reinitializing repair event handlers');
        initializeRepairEventHandlers();
    });
    
    function initializeRepairEventHandlers() {
        console.log('Initializing repair event handlers');
        const paymentButtons = document.querySelectorAll('.payment-button');
        const cancelledPaymentButtons = document.querySelectorAll('.cancelled-payment-button');
        const paymentModal = document.getElementById('payment-modal');
        const cancelledPaymentModal = document.getElementById('cancelled-payment-modal');
        const paymentModalCloseButtons = document.querySelectorAll('.payment-modal-close');
        const cancelledModalCloseButtons = document.querySelectorAll('.cancelled-modal-close');
        const deleteRepairButtons = document.querySelectorAll('.delete-repair-button');
        
        // Make sure the payment modal is hidden by default
        if (paymentModal) {
            paymentModal.style.display = 'none';
            console.log('Payment modal found and hidden');
        } else {
            console.log('Payment modal element not found');
        }
        
        // Make sure the cancelled payment modal is hidden by default
        if (cancelledPaymentModal) {
            cancelledPaymentModal.style.display = 'none';
            console.log('Cancelled payment modal found and hidden');
        }
        
        // Payment buttons event listeners
        if (paymentButtons.length > 0) {
            console.log(`Found ${paymentButtons.length} payment buttons, attaching handlers`);
            paymentButtons.forEach(button => {
                // Remove existing listeners to prevent duplicates
                button.removeEventListener('click', showPaymentModal);
                // Add the event listener
                button.addEventListener('click', showPaymentModal);
            });
        } else {
            console.log('No payment buttons found');
        }
        
        // Cancelled payment buttons event listeners
        if (cancelledPaymentButtons.length > 0) {
            console.log(`Found ${cancelledPaymentButtons.length} cancelled payment buttons, attaching handlers`);
            cancelledPaymentButtons.forEach(button => {
                // Remove existing listeners to prevent duplicates
                button.removeEventListener('click', showCancelledPaymentModal);
                // Add the event listener
                button.addEventListener('click', showCancelledPaymentModal);
            });
        }
        
        // Payment modal close buttons
        if (paymentModalCloseButtons.length > 0) {
            console.log(`Found ${paymentModalCloseButtons.length} close buttons, attaching handlers`);
            paymentModalCloseButtons.forEach(button => {
                // Remove existing listeners to prevent duplicates
                button.removeEventListener('click', hidePaymentModal);
                // Add the event listener
                button.addEventListener('click', hidePaymentModal);
            });
        }
        
        // Cancelled payment modal close buttons
        if (cancelledModalCloseButtons.length > 0) {
            console.log(`Found ${cancelledModalCloseButtons.length} cancelled modal close buttons, attaching handlers`);
            cancelledModalCloseButtons.forEach(button => {
                // Remove existing listeners to prevent duplicates
                button.removeEventListener('click', hideCancelledPaymentModal);
                // Add the event listener
                button.addEventListener('click', hideCancelledPaymentModal);
            });
        }
        
        // Close modal if clicking outside
        if (paymentModal) {
            // Remove existing listeners to prevent duplicates
            paymentModal.removeEventListener('click', handleOutsideModalClick);
            // Add the event listener
            paymentModal.addEventListener('click', handleOutsideModalClick);
        }
        
        // Close cancelled modal if clicking outside
        if (cancelledPaymentModal) {
            // Remove existing listeners to prevent duplicates
            cancelledPaymentModal.removeEventListener('click', handleOutsideCancelledModalClick);
            // Add the event listener
            cancelledPaymentModal.addEventListener('click', handleOutsideCancelledModalClick);
        }
        
        // Delete repair button event listeners
        if (deleteRepairButtons.length > 0) {
            deleteRepairButtons.forEach(button => {
                // Remove existing listeners to prevent duplicates
                button.removeEventListener('click', confirmDelete);
                // Add the event listener
                button.addEventListener('click', confirmDelete);
            });
        }
        
        // Make sure these functions are available globally
        window.showPaymentModal = showPaymentModal;
        window.hidePaymentModal = hidePaymentModal;
        window.showCancelledPaymentModal = showCancelledPaymentModal;
        window.hideCancelledPaymentModal = hideCancelledPaymentModal;
        window.handleOutsideModalClick = handleOutsideModalClick;
        window.handleOutsideCancelledModalClick = handleOutsideCancelledModalClick;
        window.confirmDelete = confirmDelete;
    }
    
    // Define the event handler functions
    function showPaymentModal(e) {
        e.preventDefault();
        const paymentModal = document.getElementById('payment-modal');
        if (paymentModal) {
            paymentModal.style.display = 'flex';
            console.log('Payment modal shown');
        }
    }
    
    function hidePaymentModal() {
        const paymentModal = document.getElementById('payment-modal');
        if (paymentModal) {
            paymentModal.style.display = 'none';
            console.log('Payment modal hidden');
        }
    }
    
    function showCancelledPaymentModal(e) {
        e.preventDefault();
        const cancelledPaymentModal = document.getElementById('cancelled-payment-modal');
        if (cancelledPaymentModal) {
            cancelledPaymentModal.style.display = 'flex';
            console.log('Cancelled payment modal shown');
        }
    }
    
    function hideCancelledPaymentModal() {
        const cancelledPaymentModal = document.getElementById('cancelled-payment-modal');
        if (cancelledPaymentModal) {
            cancelledPaymentModal.style.display = 'none';
            console.log('Cancelled payment modal hidden');
        }
    }
    
    function handleOutsideModalClick(e) {
        const paymentModal = document.getElementById('payment-modal');
        if (e.target === paymentModal) {
            paymentModal.style.display = 'none';
            console.log('Payment modal hidden (clicked outside)');
        }
    }
    
    function handleOutsideCancelledModalClick(e) {
        const cancelledPaymentModal = document.getElementById('cancelled-payment-modal');
        if (e.target === cancelledPaymentModal) {
            cancelledPaymentModal.style.display = 'none';
            console.log('Cancelled payment modal hidden (clicked outside)');
        }
    }
    
    function confirmDelete(e) {
        if (!confirm('Are you sure you want to delete this repair? This action cannot be undone.')) {
            e.preventDefault();
        }
    }
    
    // Expose these functions globally for potential external calls
    window.initializeRepairEventHandlers = initializeRepairEventHandlers;
</script>
<?php $__env->stopPush(); ?> 
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Vsmart System\vsmart sms main\resources\views/repairs/show.blade.php ENDPATH**/ ?>