<?php $__env->startSection('content'); ?>
<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Payment Settings</h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">Manage payment methods available for customers</p>
        </div>

        <!-- Payment Methods Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <?php $__currentLoopData = $paymentMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                <!-- Header -->
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <?php if($method->payment_method === 'gcash'): ?>
                            <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                                </svg>
                            </div>
                        <?php elseif($method->payment_method === 'paymaya'): ?>
                            <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3.5 6L12 10.5 8.5 8 12 5.5 15.5 8zM12 13.5L8.5 16 12 18.5 15.5 16 12 13.5z"/>
                                </svg>
                            </div>
                        <?php else: ?>
                            <div class="w-10 h-10 bg-gray-500 rounded-lg flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91 2.28.6 4.18 1.58 4.18 3.91 0 1.82-1.33 2.96-3.12 3.16z"/>
                                </svg>
                            </div>
                        <?php endif; ?>
                        <div class="ml-3">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white"><?php echo e($method->display_name); ?></h3>
                            <p class="text-sm text-gray-500 dark:text-gray-400"><?php echo e($method->description); ?></p>
                        </div>
                    </div>
                    
                    <!-- Toggle Switch -->
                    <div class="flex items-center">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" 
                                   class="sr-only peer payment-toggle" 
                                   data-method-id="<?php echo e($method->id); ?>"
                                   <?php echo e($method->is_enabled ? 'checked' : ''); ?>

                                   <?php echo e($method->payment_method === 'cash' ? 'disabled' : ''); ?>>
                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 <?php echo e($method->payment_method === 'cash' ? 'opacity-50 cursor-not-allowed' : ''); ?>"></div>
                        </label>
                    </div>
                </div>

                <!-- Status -->
                <div class="mb-4">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo e($method->is_enabled ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'); ?>">
                        <?php echo e($method->is_enabled ? 'Enabled' : 'Disabled'); ?>

                    </span>
                    <?php if($method->payment_method === 'cash'): ?>
                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                            Always Available
                        </span>
                    <?php endif; ?>
                </div>

                <!-- Configuration (if not cash) -->
                <?php if($method->payment_method !== 'cash'): ?>
                <div class="border-t border-gray-200 dark:border-gray-600 pt-4">
                    <button type="button" 
                            class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 font-medium configure-btn"
                            data-method-id="<?php echo e($method->id); ?>"
                            data-method-name="<?php echo e($method->display_name); ?>">
                        Configure Settings
                    </button>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Warning Alert -->
        <div id="warning-alert" class="mt-6 hidden">
            <div class="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-700 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                            Warning: All Digital Payment Methods Disabled
                        </h3>
                        <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                            <p>When all digital payment methods are disabled, customers will be automatically directed to cash payment option.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Configuration Modal -->
<div id="config-modal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
        
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4" id="modal-title">
                    Configure Payment Method
                </h3>
                
                <form id="config-form">
                    <div id="config-fields">
                        <!-- Dynamic fields will be inserted here -->
                    </div>
                </form>
            </div>
            
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" 
                        class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm"
                        id="save-config">
                    Save Configuration
                </button>
                <button type="button" 
                        class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                        id="cancel-config">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle payment method toggle
    document.querySelectorAll('.payment-toggle').forEach(toggle => {
        toggle.addEventListener('change', function() {
            const methodId = this.dataset.methodId;
            const isEnabled = this.checked;
            
            fetch(`/payment-settings/${methodId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    is_enabled: isEnabled
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update status badge
                    const card = this.closest('.bg-white, .dark\\:bg-gray-800');
                    const statusBadge = card.querySelector('.inline-flex.items-center.px-2\\.5');
                    
                    if (isEnabled) {
                        statusBadge.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                        statusBadge.textContent = 'Enabled';
                    } else {
                        statusBadge.className = 'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                        statusBadge.textContent = 'Disabled';
                    }
                    
                    // Check if warning should be shown
                    checkDigitalPaymentStatus();
                } else {
                    // Revert toggle if failed
                    this.checked = !isEnabled;
                    alert('Failed to update payment method status');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                this.checked = !isEnabled;
                alert('An error occurred while updating payment method status');
            });
        });
    });
    
    // Check initial status
    checkDigitalPaymentStatus();
    
    function checkDigitalPaymentStatus() {
        const digitalToggles = document.querySelectorAll('.payment-toggle:not([disabled])');
        const enabledDigital = Array.from(digitalToggles).some(toggle => toggle.checked);
        const warningAlert = document.getElementById('warning-alert');
        
        if (!enabledDigital) {
            warningAlert.classList.remove('hidden');
        } else {
            warningAlert.classList.add('hidden');
        }
    }
    
    // Handle configuration modal
    const configModal = document.getElementById('config-modal');
    const configForm = document.getElementById('config-form');
    const configFields = document.getElementById('config-fields');
    const modalTitle = document.getElementById('modal-title');
    let currentMethodId = null;
    
    document.querySelectorAll('.configure-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            currentMethodId = this.dataset.methodId;
            const methodName = this.dataset.methodName;
            
            modalTitle.textContent = `Configure ${methodName}`;
            
            // Show modal
            configModal.classList.remove('hidden');
            
            // Load current configuration
            // This would typically fetch from the server
            loadConfiguration(currentMethodId);
        });
    });
    
    document.getElementById('cancel-config').addEventListener('click', function() {
        configModal.classList.add('hidden');
    });
    
    document.getElementById('save-config').addEventListener('click', function() {
        // Save configuration logic would go here
        configModal.classList.add('hidden');
    });
    
    function loadConfiguration(methodId) {
        // This would load the current configuration from the server
        // For now, we'll show basic fields
        configFields.innerHTML = `
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    API Key
                </label>
                <input type="text" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" placeholder="Enter API key">
            </div>
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Merchant ID
                </label>
                <input type="text" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white" placeholder="Enter merchant ID">
            </div>
            <div class="mb-4">
                <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                    <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Sandbox Mode</span>
                </label>
            </div>
        `;
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Vsmart System\vsmart sms main\resources\views/payment-settings/index.blade.php ENDPATH**/ ?>